import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.ezactive.@@app_id',
  appName: '@@app_name',
  webDir: 'dist/build',
  bundledWebRuntime: false,
  ios: {
    // contentInset: 'always',
    appendUserAgent: "ios:application",
    webContentsDebuggingEnabled: true
  },
  android: {
    appendUserAgent: "android:application",
    webContentsDebuggingEnabled: true
  },
  plugins: {
    CapacitorUpdater: {
      autoUpdate: false,
    },
  },
  server: {
    url: 'https://@@site-id.web.app',
  }
};

export default config;
