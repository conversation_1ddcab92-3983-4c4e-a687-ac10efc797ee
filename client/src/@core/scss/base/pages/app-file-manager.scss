// ================================================================================================
// 	File Name: app-file-manager.scss
// 	Description: SCC file for email application page.
// 	----------------------------------------------------------------------------------------------
// 	Item Name: Vuexy - Vuejs, React, Angular, HTML & Laravel Admin Dashboard Template
// 	Author: PIXINVENT
// 	Author URL: http://www.themeforest.net/user/pixinvent
// ================================================================================================

@import '../bootstrap-extended/include'; // Bootstrap includes
@import '../components/include'; // Components includes

$file-manager-logo-height: 7.5rem;

// file manager app container
.file-manager-application {
  // file manager sidebar styles
  .sidebar-file-manager {
    width: $sidebar-width;
    height: 100%;
    background-color: $white;
    border-top-left-radius: $border-radius-sm;
    border-bottom-left-radius: $border-radius-sm;
    transition: all 0.3s ease, background 0s;

    // styles for card-inner
    .sidebar-inner {
      height: inherit;

      .dropdown-actions {
        width: 100%;
        padding: 1.5rem 1.5rem 1.8rem;
      }

      .sidebar-close-icon {
        position: absolute;
        top: 0.25rem;
        right: 0.25rem;
        font-size: $font-size-xl;
        z-index: 5;
        cursor: pointer;
      }

      .add-file-btn {
        & ~ .dropdown-menu {
          width: 85%;
          &:before {
            display: none;
          }
        }
      }

      // styles for card inner list
      .sidebar-list {
        position: relative;
        height: calc(100% - 85px); // search section height 85px
        .my-drive {
          padding: 0.58rem 0;
          .jstree-node {
            .jstree-icon {
              background-position: 0.5rem;
              background-size: 1.3rem;
            }
            .jstree-anchor > .jstree-themeicon {
              margin-right: 0.5rem;
            }
            &.jstree-closed {
              > .jstree-icon {
                background-image: url(str-replace(
                  str-replace($chevron-right, 'currentColor', $headings-color),
                  '#',
                  '%23'
                ));
              }
            }
            &.jstree-open {
              > .jstree-icon {
                background-image: url(str-replace(
                  str-replace($chevron-down, 'currentColor', $headings-color),
                  '#',
                  '%23'
                ));
              }
            }
          }
        }

        .list-group-labels,
        .storage-status {
          margin-top: 2.2rem;
        }

        .list-group-item {
          padding: 0.58rem 1.9rem;
          font-weight: 500;
          border: 0;
          border-radius: 0;
          border-left: 2px solid transparent;
          &:hover {
            z-index: 0 !important;
          }
          + .list-group-item.active {
            margin-top: 0;
          }
        }
        .active {
          background-color: transparent;
          border-color: $primary;
          color: $primary;
        }
      }
    }

    // show sidebar on click
    &.show {
      transition: all 0.25s ease, background 0s !important;
      transform: translateX(0) !important;
      z-index: 10;
    }
  }

  // styles for content area wrapper
  .content-area-wrapper {
    border: 1px solid $border-color;
    border-radius: $border-radius-sm;

    .content-right {
      .content-wrapper {
        padding: 0;
      }
    }

    // styles for file manger app area
    .file-manager-main-content {
      border-left: 1px solid $border-color;
      height: inherit;

      // styles for searchbar
      .file-manager-content-header {
        padding: 0.4rem 1rem;
        border-bottom: 1px solid $border-color;
        background-color: $white;
        border-top-right-radius: $border-radius;

        .file-manager-toggler {
          cursor: pointer;
        }

        input {
          border-color: transparent;
          box-shadow: none;
        }

        .file-actions:not(.show) {
          display: none;
        }
      }

      // styles for main content
      .file-manager-content-body {
        position: relative;
        padding: 1.5rem 1.5rem 0;
        height: calc(100% - 50px); // search area height
        background-color: $white;
        .view-container {
          display: flex;
          flex-wrap: wrap;
          .file-manager-item {
            border: 1px solid $border-color;
            margin-bottom: 1.6rem;
            box-shadow: none;
            margin-right: 1rem;
            transition: none;
            cursor: pointer;

            .card-body {
              padding: 1rem;
              padding-bottom: 0.5rem;
            }

            .custom-control-input:not(:checked) ~ .custom-control-label:before {
              background-color: transparent;
            }

            .file-logo-wrapper {
              padding: 1rem;
              height: $file-manager-logo-height;
              background-color: $body-bg;
              .feather-folder {
                stroke: $gray-100;
              }
            }

            .dropdown-menu {
              transform: none;
              &:before {
                display: none;
              }
            }

            .content-wrapper {
              display: flex;
              flex-direction: row;
              justify-content: space-between;
              align-items: center;
              height: auto;
            }

            .file-date {
              flex-grow: 1;
              margin-bottom: 0;
            }

            &.selected {
              border-color: $primary;
            }
          }

          .file-name {
            width: calc(100% - 26rem);
            min-height: 1rem;
            font-weight: 600;
            flex-grow: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .files-section-title {
            width: 100%;
          }

          &.list-view {
            flex-direction: column;

            .files-section-title {
              display: none;
            }
            .files-header {
              display: flex;
              justify-content: space-between;
              margin-left: 7.2rem;
              margin-bottom: 1rem;

              p {
                font-weight: 600;
              }

              .file-last-modified,
              .file-item-size {
                margin-right: 3rem;
              }
            }

            .file-manager-item {
              flex-direction: row;
              flex: 0 0 100%;
              align-items: center;
              max-width: 100%;
              margin-bottom: 0.75rem;
              margin-right: 0;
              .file-logo-wrapper {
                padding-right: 0;
                width: auto;
                height: auto;
                background-color: transparent !important;

                img {
                  height: 1.5rem;
                }

                .feather-folder,
                .feather-arrow-up {
                  width: 19px;
                  height: 18px;
                }

                .dropdown {
                  position: absolute;
                  right: 1rem;
                }
              }
              .custom-checkbox {
                margin: 0 0.4rem 0 1.75rem;
              }

              .file-accessed {
                display: none;
              }

              .file-size {
                width: 5.71rem;
                flex-grow: 1;
                margin-bottom: 0;
              }
            }
            .folder {
              &.level-up {
                .file-logo-wrapper {
                  margin-left: 3.5rem;
                }
              }
            }
          }

          &:not(.list-view) {
            .files-header {
              display: none;
            }

            .file-manager-item {
              .content-wrapper {
                margin-bottom: 0rem;
              }

              .file-date {
                display: none;
              }
              .file-size {
                color: $text-muted;
                font-size: 85%;
              }
              &:not(.selected):not(:hover) {
                .custom-checkbox,
                .toggle-dropdown {
                  opacity: 0;
                }
              }
              .feather-folder {
                height: 32px;
                width: 35px;
              }
              &.folder.level-up {
                display: none !important;
              }
              .custom-checkbox {
                position: absolute;
                top: 1rem;
                left: 1rem;
              }
            }
          }
        }
      }
    }
  }
}

// media query for max-width of 1200px
@media screen and (max-width: 1200px) {
  .file-manager-application {
    .content-right {
      width: 100%;
    }
    .content-body {
      margin-left: 0 !important;
    }
    .content-area-wrapper {
      .file-manager-main-content {
        border-left: 0;
      }
    }

    .sidebar-left {
      .sidebar {
        z-index: 10;

        .sidebar-file-manager {
          transform: translateX(-112%);
          transition: all 0.25s ease;
          position: absolute;
        }
      }
    }
  }
}

@include media-breakpoint-down(sm) {
  .file-manager-application {
    .view-container {
      .file-manager-item {
        flex: 47%;
      }
    }
  }
}

@include media-breakpoint-down(xs) {
  .file-manager-application {
    .content-area-wrapper {
      .file-manager-main-content {
        .file-manager-content-header {
          padding-left: 0.5rem;

          .file-actions {
            .dropdown {
              padding-left: 0.5rem;
              padding-right: 0.5rem;
            }
          }
        }
      }
      .view-container {
        .file-manager-item {
          flex: 0 0 100%;
          max-width: 100%;
        }
        &.list-view {
          .file-date,
          .file-last-modified,
          .file-size,
          .file-item-size {
            display: none !important;
          }
        }
      }
    }
  }
}

@include media-breakpoint-up(xs) {
  .file-manager-application {
    .view-container {
      .file-manager-item {
        flex: 0 0 47%;
        max-width: 50%;
      }
    }
  }
}

@include media-breakpoint-up(sm) {
  .file-manager-application {
    .view-container {
      .file-manager-item {
        flex: 0 0 23%;
        max-width: 25%;
      }
    }
  }
}
