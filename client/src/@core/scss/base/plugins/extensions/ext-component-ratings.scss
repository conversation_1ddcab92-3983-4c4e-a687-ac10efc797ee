@import '../../bootstrap-extended/include'; // Bootstrap includes
@import '../../components/include'; // Components includes

.jq-ry-container:not(.multi-color-ratings) {
  .jq-ry-normal-group {
    i,
    svg {
      fill: $gray-100;
    }
  }
  .jq-ry-rated-group {
    i,
    svg {
      fill: $warning;
    }
  }
}

// Dark Layout
.dark-layout {
  .jq-ry-container:not(.multi-color-ratings) {
    .jq-ry-normal-group {
      i,
      svg {
        fill: $theme-dark-text-muted-color;
      }
    }
  }
}

// RTL
[data-textdirection='rtl'] {
  .jq-ry-container:not(.multi-color-ratings) {
    .jq-ry-normal-group {
      i,
      svg {
        fill: $warning;
      }
    }
    .jq-ry-rated-group {
      i,
      svg {
        fill: $gray-100;
      }
    }
  }
  .dark-layout {
    .jq-ry-container:not(.multi-color-ratings) {
      .jq-ry-rated-group {
        i,
        svg {
          fill: $theme-dark-text-muted-color;
        }
      }
    }
  }
}
