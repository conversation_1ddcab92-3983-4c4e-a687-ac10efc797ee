import {
  Compo<PERSON>,
  Inject,
  <PERSON><PERSON><PERSON><PERSON>,
  On<PERSON>nit,
  ElementRef,
  Renderer2,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { Title } from '@angular/platform-browser';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import * as Waves from 'node-waves';
import { App } from '@capacitor/app';
import { StatusBar, Style } from '@capacitor/status-bar';
import Swal from 'sweetalert2';

import { CoreMenuService } from '@core/components/core-menu/core-menu.service';
import { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';
import { CoreConfigService } from '@core/services/config.service';
import { CoreLoadingScreenService } from '@core/services/loading-screen.service';
import { CoreTranslationService } from '@core/services/translation.service';
import { Location } from '@angular/common';
import { menu } from 'app/menu/menu';
import { AuthService } from './services/auth.service';

import { Router } from '@angular/router';
import { LoadingService } from './services/loading.service';
import { FcmService } from './services/fcm.service';
import { NotificationsService } from './layout/components/navbar/navbar-notification/notifications.service';
import { SettingsService } from './services/settings.service';
import { Capacitor } from '@capacitor/core';
import { NavigationService } from './services/navigation.service';
import { Camera } from '@capacitor/camera';
import { Platform } from '@angular/cdk/platform';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CommonsService } from './services/commons.service';
import { environment } from 'environments/environment';
import { AppConfig } from './app-config';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppComponent implements OnInit, OnDestroy {
  coreConfig: any;
  menu: any;
  defaultLanguage: 'en'; // This language will be used as a fallback when a translation isn't found in the current language
  appLanguage: 'zh_HK'; // Set application default language i.e fr
  showBtnInstallPwa: boolean = false;
  deferredPrompt: any;
  // Private
  private _unsubscribeAll: Subject<any>;

  //Get element by angular view child
  @ViewChild('modal_loading_screen', { static: true })
  modal_loading_screen: ElementRef;

  @ViewChild('modal_install_pwa', { static: true })
  modal_install_pwa: ElementRef;

  /**
   * Constructor
   *
   * @param {DOCUMENT} document
   * @param {Title} _title
   * @param {Renderer2} _renderer
   * @param {ElementRef} _elementRef
   * @param {CoreConfigService} _coreConfigService
   * @param {CoreSidebarService} _coreSidebarService
   * @param {CoreLoadingScreenService} _coreLoadingScreenService
   * @param {CoreMenuService} _coreMenuService
   * @param {CoreTranslationService} _coreTranslationService
   * @param {TranslateService} _translateService
   */
  constructor(
    @Inject(DOCUMENT) private document: any,
    private _title: Title,
    public platform: Platform,
    private _renderer: Renderer2,
    private _elementRef: ElementRef,
    public _coreConfigService: CoreConfigService,
    private _coreSidebarService: CoreSidebarService,
    private _coreMenuService: CoreMenuService,
    private _translateService: TranslateService,
    private _authService: AuthService,
    private _router: Router,
    private _coreLoadingScreenService: CoreLoadingScreenService,
    public _loadingService: LoadingService,
    public _fcmService: FcmService,
    public _notificationService: NotificationsService,
    public _settingsService: SettingsService,
    private location: Location,
    public _modalService: NgbModal,
    public _navigationService: NavigationService,
    public _commonsService: CommonsService
  ) {
    _authService.currentUser.subscribe((x) => {
      if (x) {
        _notificationService.currentUserValue = x;
        _commonsService.simpleUploadConfig = {
          // The URL that the images are uploaded to.
          uploadUrl: `${environment.apiUrl}/send-messages/upload`,
          headers: {
            Accept: 'application/json, text/plain, */*',
            'X-CSRF-TOKEN': 'CSRF-Token',
            Authorization: `Bearer ${x.token}`,
            'X-project-id': AppConfig.PROJECT_ID,
          },
        };
      }
    });
    // Get the application main menu
    this.menu = JSON.parse(JSON.stringify(menu));

    // Register the menu to the menu service
    this._coreMenuService.register('main', this.menu);

    // Set the main menu as our current menu
    this._coreMenuService.setCurrentMenu('main');

    // Add languages to the translation service
    this._translateService.addLangs(['en', 'zh_HK']);

    // This language will be used as a fallback when a translation isn't found in the current language
    this._translateService.setDefaultLang('en');

    // Set the private defaults
    this._unsubscribeAll = new Subject();
    if (_authService.currentUserValue) {
      _settingsService.getInitSettings(false);
    }
  }

  // Lifecycle hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit(): void {
    this._authService.getProfile().subscribe((data) => {
      // console.log('data', data);
      this._coreMenuService.unregister('main');
      this._coreMenuService.register('main', menu);
      this._coreMenuService.setCurrentMenu('main');
    });
    // this._fcmService.listen();
    this._notificationService.listen();
    // This code handles the functionality of the back button
    App.addListener('backButton', ({ canGoBack }) => {
      console.log('canGoBack', canGoBack);

      // Check if there is anything in the browsing history
      if (!canGoBack) {
        // Exit the app if not
        App.exitApp();
      } else {
        this._navigationService.back();
      }
    });

    // Init wave effect (Ripple effect)
    Waves.init();
    this._loadingService.modalLoading = this.modal_loading_screen;
    // Subscribe to config changes
    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.coreConfig = config;
        const appLanguage = this.coreConfig.app.appLanguage || 'en';
        this._translateService.use(appLanguage);

        setTimeout(() => {
          this._translateService.setDefaultLang('en');
          this._translateService.setDefaultLang(appLanguage);
        });

        // Remove default classes first
        this._elementRef.nativeElement.classList.remove(
          'vertical-layout',
          'vertical-menu-modern',
          'horizontal-layout',
          'horizontal-menu'
        );
        // Add class based on config options
        if (this.coreConfig.layout.type === 'vertical') {
          this._elementRef.nativeElement.classList.add(
            'vertical-layout',
            'vertical-menu-modern'
          );
        } else if (this.coreConfig.layout.type === 'horizontal') {
          this._elementRef.nativeElement.classList.add(
            'horizontal-layout',
            'horizontal-menu'
          );
        }

        // Navbar
        //--------

        // Remove default classes first
        this._elementRef.nativeElement.classList.remove(
          'navbar-floating',
          'navbar-static',
          'navbar-sticky',
          'navbar-hidden'
        );

        // Add class based on config options
        if (this.coreConfig.layout.navbar.type === 'navbar-static-top') {
          this._elementRef.nativeElement.classList.add('navbar-static');
        } else if (this.coreConfig.layout.navbar.type === 'fixed-top') {
          this._elementRef.nativeElement.classList.add('navbar-sticky');
        } else if (this.coreConfig.layout.navbar.type === 'floating-nav') {
          this._elementRef.nativeElement.classList.add('navbar-floating');
        } else {
          this._elementRef.nativeElement.classList.add('navbar-hidden');
        }

        // Footer
        //--------

        // Remove default classes first
        this._elementRef.nativeElement.classList.remove(
          'footer-fixed',
          'footer-static',
          'footer-hidden'
        );

        // Add class based on config options
        if (this.coreConfig.layout.footer.type === 'footer-sticky') {
          this._elementRef.nativeElement.classList.add('footer-fixed');
        } else if (this.coreConfig.layout.footer.type === 'footer-static') {
          this._elementRef.nativeElement.classList.add('footer-static');
        } else {
          this._elementRef.nativeElement.classList.add('footer-hidden');
        }

        // Blank layout
        if (
          this.coreConfig.layout.menu.hidden &&
          this.coreConfig.layout.navbar.hidden &&
          this.coreConfig.layout.footer.hidden
        ) {
          this._elementRef.nativeElement.classList.add('blank-page');
          // ! Fix: Transition issue while coming from blank page
          this._renderer.setAttribute(
            this._elementRef.nativeElement.getElementsByClassName(
              'app-content'
            )[0],
            'style',
            'transition:none'
          );
        } else {
          this._elementRef.nativeElement.classList.remove('blank-page');
          // ! Fix: Transition issue while coming from blank page
          setTimeout(() => {
            if (
              this._elementRef.nativeElement.getElementsByClassName(
                'app-content'
              )[0]
            ) {
              this._renderer.setAttribute(
                this._elementRef.nativeElement.getElementsByClassName(
                  'app-content'
                )[0],
                'style',
                'transition:300ms ease all'
              );
            }
          }, 100);
          // If navbar hidden
          if (this.coreConfig.layout.navbar.hidden) {
            this._elementRef.nativeElement.classList.add('navbar-hidden');
          }
          // Menu (Vertical menu hidden)
          if (this.coreConfig.layout.menu.hidden) {
            this._renderer.setAttribute(
              this._elementRef.nativeElement,
              'data-col',
              '1-column'
            );
          } else {
            this._renderer.removeAttribute(
              this._elementRef.nativeElement,
              'data-col'
            );
          }
          // Footer
          if (this.coreConfig.layout.footer.hidden) {
            this._elementRef.nativeElement.classList.add('footer-hidden');
          }
        }

        // Skin Class (Adding to body as it requires highest priority)
        if (
          this.coreConfig.layout.skin !== '' &&
          this.coreConfig.layout.skin !== undefined
        ) {
          this.document.body.classList.remove(
            'default-layout',
            'bordered-layout',
            'dark-layout',
            'semi-dark-layout'
          );
          this.document.body.classList.add(
            this.coreConfig.layout.skin + '-layout'
          );
        }
      });

    // Set the application page title
    this._title.setTitle(this.coreConfig.app.appTitle);

    // create trigger when updating user
    this._authService.currentUser.subscribe((stage: any) => {
      if (stage != null) {
        this._coreConfigService.setConfig(
          { app: { appLanguage: stage.language } },
          { emitEvent: true }
        );
        setTimeout(() => {
          this._translateService.use(stage.language);

        }, 200);
        // this._router.navigate(["/home"]);
      }
    });

    this._coreConfigService.setConfig({
      layout: {
        type: 'vertical',
      },
    });

    this.getStreamConfig();

    // check url is egress
    // egress is streaming token
    if (!this.location.path().includes('egress')) {
      console.log('not egress');
      this.requestNotificationPermission();
      this.requestMediaPermission();
      this._settingsService.getNotificationSettings().subscribe((data) => {
        console.log(data);
        const title = data.title;
        const body = data.body;
        const is_on = data.is_on;
        const notification = sessionStorage.getItem('notification');

        if (!is_on) return;
        if (notification == 'true') return;

        Swal.fire({
          title: title,
          text: body,
          confirmButtonText: 'OK',
        }).then((result) => {
          if (result.isConfirmed) {
            sessionStorage.setItem('notification', 'true');
            Swal.close();
          }
        });
      });
    } else {
      console.log('egress');
    }
  }

  hideStatusBar = async () => {
    await StatusBar.setBackgroundColor({ color: '#000' });
    await StatusBar.hide();
  };


  async getStreamConfig() {
    console.log('getStreamConfig');
    this._settingsService.getSettingNoAuth().subscribe((res) => {
      let livekit = res.find(
        (setting) => setting.key === AppConfig.SETTINGS_KEYS.LIVEKIT
      )
      const name_settings = res.find(
        (setting) => setting.key === AppConfig.SETTINGS_KEYS.NAME_SETTINGS
      );
      if (name_settings) {
        localStorage.setItem('name_settings', JSON.stringify(name_settings.value));
      }

      if (livekit) {
        localStorage.setItem('livekit', JSON.stringify(livekit.value));
      }
    })
  }

  async requestNotificationPermission() {
     // Initialize push notifications
     this._fcmService.initPush();

     // Listen for incoming messages
     this._fcmService.listenToMessages();
 
     // Optionally log the current FCM token
     const token = this._fcmService.getToken();
     console.log('FCM Token:', token);
   
  }

  async requestMediaPermission() {
    if (Capacitor.isNativePlatform()) {
      this.hideStatusBar();
      let status = await Camera.checkPermissions();
      switch (status.camera) {
        case 'denied':
        case 'prompt':
        case 'prompt-with-rationale':
          return await Camera.requestPermissions();
        default:
          return status.camera;
      }
    } else {
      // let res = await navigator.mediaDevices.getUserMedia({
      //   video: true,
      //   audio: true,
      // });
      // res.getTracks().forEach((track) => {
      //   track.stop();
      // });
      // return res;
    }
  }

  ngAfterViewInit() {
    // setTimeout(() => {
    //   if (Capacitor.isNativePlatform()) return;
    //   if ('serviceWorker' in navigator && 'PushManager' in window) {
    //     window.addEventListener('beforeinstallprompt', (event) => {
    //       event.preventDefault();
    //       this.deferredPrompt = event;
    //       this.showBtnInstallPwa = true;
    //     });
    //     if (
    //       this.platform.IOS ||
    //       (this.platform.SAFARI && (window as any).navigator.standalone)
    //     ) {
    //       this.showBtnInstallPwa = false;
    //     }
    //     console.log('supported', this.platform);
    //   } else {
    //     console.log(
    //       'not supported',
    //       (window as any).navigator.standalone,
    //       this.platform
    //     );
    //     this.showBtnInstallPwa = true;
    //     if (
    //       this.platform.IOS ||
    //       (this.platform.SAFARI && (window as any).navigator.standalone)
    //     ) {
    //       this.showBtnInstallPwa = false;
    //     }
    //   }
    // }, 1000);
  }
  async onInstallCLick() {
    console.log('install button clicked');
    if (!this.platform.IOS && !this.platform.SAFARI) {
      console.log('android', this.deferredPrompt);
      if (this.deferredPrompt) {
        const result = await this.deferredPrompt.prompt();
        console.log(`Install prompt was: ${result.outcome}`);
      }
    } else {
      this._modalService.dismissAll();
      console.log('ios');
      let modalRef = this._modalService.open(this.modal_install_pwa, {
        centered: true,
        backdrop: 'static',
        size: 'md',
      });
    }
  }
  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  // Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Toggle sidebar open
   *
   * @param key
   */
  toggleSidebar(key): void {
    this._coreSidebarService.getSidebarRegistry(key).toggleOpen();
  }
}
