import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { RegistrationService } from 'app/services/registration.service';

@Component({
  selector: 'card-season',
  templateUrl: './card-season.component.html',
  styleUrls: ['./card-season.component.scss'],
})
export class CardSeasonComponent {
  @Input() season: any;
  constructor(public router: Router) {}
  viewDetail() {
    this.router.navigate([`registration/season/${this.season.id}`]);
  }
}
