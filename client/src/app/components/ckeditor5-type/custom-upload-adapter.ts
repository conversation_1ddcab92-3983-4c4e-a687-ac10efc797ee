import { AppConfig } from 'app/app-config';
import { SendMessagesService } from 'app/services/send-messages.service';
import Swal from 'sweetalert2';
import { title } from 'process';
import { TranslateService } from '@ngx-translate/core';
import { S3Service } from 'app/services/s3.service';
import { environment } from 'environments/environment';

export class CustomUploadAdapter {
  loader: any;
  maxSize: number = AppConfig.MAX_UPLOAD_SIZE * 1024 * 1024;

  constructor(
    loader: any,
    private _s3Service: S3Service,
    private _translateService: TranslateService
  ) {
    this.loader = loader;
  }

  upload() {
    return this.loader.file.then((file: File) => {
      return new Promise((resolve, reject) => {

        this._s3Service.getMaxUploadSize().subscribe((response) => {
          const maxUploadSize = response['maxUploadSizeInBytes'];

          if (file.size > maxUploadSize) {
            Swal.fire({
              icon: 'error',
              title: this._translateService.instant('Error'),
              text: this._translateService.instant(
                `File too large! Maximum {{maxUploadSize}}MB allowed`,
                { maxUploadSize: response['maxUploadSizeInMB'] }
              )
            });
            reject();
          } else {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
              // resolve({
              //   default: reader.result as string,
              // });
            };
            reader.onerror = (error) => reject(error);

            const formData = new FormData();
            formData.append('upload', file);
            formData.append('dir', 'ckeditor');

            this._s3Service.uploadImage(formData).subscribe({
              next: (response: any) => {
                resolve({
                  default: `${environment.apiUrl}/s3?key=${response.files.filename}`
                });
              },
              error: (err) => {

                const uploadError = err.fieldErrors?.find(fieldError => fieldError.name === 'upload') ?? null;

                console.log('uploadError', uploadError);

                if (uploadError) {
                  Swal.fire({
                    icon: 'error',
                    title: this._translateService.instant('Warning'),
                    text: this._translateService.instant(uploadError.status, {
                      maxUploadSize: uploadError.maxUploadSize
                    })
                  });
                }

                if (err.errors.hasOwnProperty('upload')) {
                  Swal.fire({
                    icon: 'error',
                    title: this._translateService.instant('Error'),
                    text: this._translateService.instant(
                      `Have some problem `,
                      { maxUploadSize: AppConfig.MAX_UPLOAD_SIZE }
                    )
                  });
                }

                reject();
              }
            });
          }
        });
      });
    });
  }

  abort() {
  }

  private removeImageFromEditor(imageUrl: string) {
    document.querySelectorAll('img').forEach((img: HTMLImageElement) => {
      if (img.src === imageUrl) {
        img.remove();
      }
    });
  }
}
