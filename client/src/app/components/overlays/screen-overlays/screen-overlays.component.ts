import { Component, EventEmitter, Input } from '@angular/core';

@Component({
  selector: 'screen-overlays',
  templateUrl: './screen-overlays.component.html',
  styleUrls: ['./screen-overlays.component.scss'],
})
export class ScreenOverlaysComponent {
  @Input() resizeEvent: EventEmitter<any>;

  constructor() {}
  ngOnInit() {
    this.resizeEvent.subscribe(() => {
      this.resizeScreen();
    });
  }

  ngAfterViewInit() {
    this.resizeScreen();
    // listen for resize event
    window.addEventListener('resize', () => {
      this.resizeScreen();
    });
  }

  resizeScreen() {
    let screen = document.getElementsByClassName('screen')[0] as HTMLElement;
    // resize screen to fit parent container
    // scale screen to fit parent container
    let ratio = 16 / 9;
    let parent = screen.parentElement.parentElement;
    let prent_width = parent.clientWidth;
    let prent_height = parent.clientHeight;
    let width = prent_width;
    let height = prent_height;
    if (width / height > ratio) {
      width = height * ratio;
    } else {
      height = width / ratio;
    }
    let scale_w = width / 1920;
    let scale_h = height / 1080;
    screen.style.transform = `scale(${scale_w}, ${scale_h})`;
    // get element position
    let screen_rect = screen.getBoundingClientRect();
    let screen_width = screen_rect.width;
    let screen_height = screen_rect.height;
    let screen_x = screen_rect.x;
    let screen_y = screen_rect.y;
    screen.style.transformOrigin = '0px 0px';
    // calculate top and left position
    let top = 0;
    let left = 0;

    if (screen_width < prent_width) {
      left = (prent_width - screen_width) / 2;
    }

    if (screen_height < prent_height) {
      top = (prent_height - screen_height) / 2;
    }

    screen.style.top = `${top}px`;
    screen.style.left = `${left}px`;
  }
}
