import { Component } from '@angular/core';
import { OverlaysService } from '../overlays.service';
import { LivekitService, RoomMetadata } from 'app/services/livekit.service';
declare var $: any;
declare var window: any;
@Component({
  selector: 'sponsors-overlay',
  templateUrl: './sponsors-overlay.component.html',
  styleUrls: ['./sponsors-overlay.component.scss'],
})
export class SponsorsOverlayComponent {
  roomMetadata: RoomMetadata;
  images: any = [];
  constructor(
    public overlayService: OverlaysService,
    public livekitService: LivekitService
  ) {
    livekitService.roomMetadataSubject.subscribe((roomMetadata) => {
      if (roomMetadata) {
        this.roomMetadata = roomMetadata;
        if (roomMetadata && roomMetadata?.overlay_data?.sponsors_logo) {
          this.images = roomMetadata.overlay_data.sponsors_logo;
        }
      }
    });
  }

  ngOnInit() {}
  ngAfterViewInit() {
    // flip each sponsor image in images after 5 seconds
    let i = 0;
    setInterval(() => {
      if (
        this.images.length > 0 &&
        this.images.length >= i &&
        this.roomMetadata?.overlay_data?.is_show_sponsor_logo
      ) {
        this.flipSponsors(this.images[i]);
        i = (i + 1) % this.images.length;
      }
    }, 5000);
  }
  // flip sponsor images
  flipSponsors(imgage: any) {
    let sponsors_container = document.getElementById(
      'sponsors-images-container'
    );
    // add class flip-360 to the container
    sponsors_container.classList.add('flip-180');
    let img = sponsors_container.getElementsByTagName('img');
    // remove class flip-360 from the container after 1 second
    setTimeout(() => {
      img[0].src = imgage.src;
      img[0].alt = imgage.alt;
      sponsors_container.classList.remove('flip-180');
    }, 500);
  }
}
