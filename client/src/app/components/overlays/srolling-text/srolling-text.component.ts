import { Component, ViewEncapsulation } from '@angular/core';
import { LivekitService, RoomMetadata } from 'app/services/livekit.service';
import { OverlaysService } from '../overlays.service';

@Component({
  selector: 'srolling-text',
  templateUrl: './srolling-text.component.html',
  styleUrls: ['./srolling-text.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class SrollingTextComponent {
  roomMetadata: RoomMetadata;
  innerHTML: string = '';
  constructor(
    public overlayService: OverlaysService,
    public livekitService: LivekitService
  ) {
    livekitService.roomMetadataSubject.subscribe((roomMetadata) => {
      if (roomMetadata) {
        this.roomMetadata = roomMetadata;
        if (roomMetadata && roomMetadata?.overlay_data?.scroll_text) {
          this.innerHTML = roomMetadata.overlay_data.scroll_text.text;
        }
      }
    });
  }
}
