<div *ngFor="let field of field.fieldGroup; let i = index;" class="row">
    <formly-field class="col" [field]="field"></formly-field>
    <div class="col-sm-2 p-0 d-flex align-items-center">
        <button class="btn text-danger p-50" type="button" (click)="remove(i)">
            <i class="fa fa-trash"></i>
        </button>
    </div>
</div>
<div class="my-50">
    <button class="btn btn-primary" type="button" (click)="add()" [innerHTML]="to.addText"></button>
</div>