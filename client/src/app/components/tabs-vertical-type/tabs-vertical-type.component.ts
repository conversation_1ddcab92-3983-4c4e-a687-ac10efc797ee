import { Component, EventEmitter, Input } from '@angular/core';
import { FieldType, FormlyFieldConfig } from '@ngx-formly/core';
@Component({
  selector: 'formly-field-tabs',
  templateUrl: './tabs-vertical-type.component.html',
})
export class FormlyFieldTabsVertical extends FieldType {
  activeID = 0;
  onNext: any;
  onPrev: any;
  ngOnInit() {
    if (this.field.props.hasOwnProperty('onNext')) {
      this.onNext = this.field.props.onNext;
    }

    if (this.field.props.hasOwnProperty('onPrev')) {
      this.onPrev = this.field.props.onPrev;
    }
  }
  isValid(field: FormlyFieldConfig): boolean {
    if (field.key) {
      return field.formControl.valid;
    }
    return field.fieldGroup
      ? field.fieldGroup.every((f) => this.isValid(f))
      : true;
  }

  nextTab(ngbNav: any, current_index: number) {
    console.log('Next current_index', current_index);
    if (current_index >= 0) {
      this.form.markAsPristine();
      this.activeID = current_index + 1;
      ngbNav.select(this.activeID);
      if (this.onNext) this.onNext(this.activeID, this.model);
    }
  }

  prevTab(ngbNav: any, current_index: number) {
    if (current_index > 0) {
      this.activeID = current_index - 1;
      ngbNav.select(this.activeID);
      if (this.onPrev) this.onPrev(this.activeID, this.model);
    }
  }
}
