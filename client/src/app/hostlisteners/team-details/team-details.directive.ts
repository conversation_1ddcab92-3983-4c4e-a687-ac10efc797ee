import { Directive, EventEmitter, HostListener, Output } from '@angular/core';
import { Router } from '@angular/router';
import { NavigationService } from 'app/services/navigation.service';

@Directive({
  selector: '[teamDetails]',
})
export class TeamDetailsDirective {
  constructor(private navigation: NavigationService, public _router: Router) {}
  @Output() teamDetailsClick = new EventEmitter();
  @HostListener('click', ['$event.target.id'])
  onClick(id: any): void {
    console.log('team id', id);
    if (!id || id=='null') return;
    this._router.navigate(['/leagues/fixtures-results', 'team', id]);
    this.teamDetailsClick.emit(id);
  }
}
