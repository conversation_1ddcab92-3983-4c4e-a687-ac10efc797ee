export interface StageMatch {
    id: number;
    home_team_id: number;
    away_team_id: number;
    home_score: number;
    away_score: number;
    home_penalty: number;
    away_penalty: number;
}

export interface HeadToHeadStats {
    points: number;
    goals: number;
    goals_difference: number;
}

export interface Club {
    id: number;
    name: string;
    logo: string;
    code: string;
    is_active: number;
}

export interface TeamInfo {
    id: number;
    name: string;
    group_id: number;
    club_id: number;
    club: Club;
}

export interface TeamData {
    id: number;
    stage_id: number;
    team_id: number;
    group: any;
    order: number;
    created_at: string | null;
    updated_at: string | null;
    team: TeamInfo;
    no_matches: number;
    no_wins: number;
    no_draws: number;
    no_losses: number;
    goals_for: number;
    goals_against: number;
    goals_difference: number;
    points: number;
    head_to_head: {
        [opponentId: string]: HeadToHeadStats;
    };
    _h2h_points: number;
    _h2h_gd: number;
    _h2h_goals: number;
}
