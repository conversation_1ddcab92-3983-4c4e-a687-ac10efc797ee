import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';


@Component({
  selector: 'app-modal-input-cancel-reason',
  templateUrl: './modal-input-cancel-reason.component.html',
  styleUrls: ['./modal-input-cancel-reason.component.scss']
})
export class ModalInputCancelReasonComponent implements OnInit {
  @Input() registrations: any;
  form = new FormGroup({});
  modalRef: any;
  model = { reason: '' }; // Define the 'reason' property here with an initial value
  options: FormlyFormOptions = {};

  ngOnInit(): void {
    console.log(this.registrations);
  }
  constructor(public activeModal: NgbActiveModal) {}


  fields: FormlyFieldConfig[] = [
    {
      key: 'reason',
      type: 'textarea',
      templateOptions: {
        label: 'Reason',
        placeholder: 'Enter your reason here',
        rows: 4,
      },
    },
  ];

  dismissModal() {
    this.activeModal.dismiss('Close click');
  }

  submit() {
    if (this.form.valid) {
      // Perform your submit logic here
      this.activeModal.close(this.model);
    }
  }


}