import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';
import { TranslateService } from '@ngx-translate/core';
import { AppConfig, coreConfig } from 'app/app-config';
import { RegistrationService } from 'app/services/registration.service';
import { environment } from 'environments/environment';
@Component({
  selector: 'app-modal-message-reminder',
  templateUrl: './modal-message-reminder.component.html',
  styleUrls: ['./modal-message-reminder.component.scss']
})
export class ModalMessageReminderComponent implements OnInit {
  @Input() registrations: any;
  form = new FormGroup({});
  modalRef: any;
  model = {
    title: "Player registration is incomplete",
    message: '',

  }
  fields: FormlyFieldConfig[] = [
    {
      key: 'title',
      type: 'input',
      props: {
        type: 'text',
        label: this._translateService.instant('Title'),
        required: true,
      },
    },
    {
      key: 'content',
      type: 'ckeditor5',
      props: {
        required: true,
        label: this._translateService.instant('Content'),
        config: {
          simpleUpload: {
            // The URL that the images are uploaded to.
            uploadUrl: `${environment.apiUrl}/clubs/editor`,
          },
          placeholder: this._translateService.instant('Type the content here'),
          htmlSupport: {
            allow: [
              {
                name: /.*/,
                attributes: true,
                classes: true,
                styles: true,
              },
            ],
          },
          htmlEmbed: {
            showPreviews: true,
          },
          mention: {
            feeds: [
              {
                marker: '{',
                feed: this.getFeedItems,
                minimumCharacters: 1,
              },
            ],
          },
        },
      },
      defaultValue: `<p><strong>Dear parent,</strong></p>
      <p>Please note that the registration for player registration is currently incomplete and pending the successful submission of the following information:</p>
      <ul>
        <li>Please go to Registration in the app and resubmit the information.</li>
      </ul>`
    }
  ];
  options: FormlyFormOptions = {};
  constructor(
    private activeModal: NgbActiveModal,
    private registrationService: RegistrationService,
    private _translateService: TranslateService
  ) { }

  getFeedItems(queryText) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const itemsToDisplay = [
          '{{user_first_name}}',
          '{{user_last_name}}',
          '{{user_email}}',
        ]
          .filter(isItemMatching)
          .slice(0, 10);

        resolve(itemsToDisplay);
      }, 100);
    });

    function isItemMatching(item) {
      const searchString = queryText.toLowerCase();
      return item.toLowerCase().includes(searchString);
      // ||        item.id.toLowerCase().includes(searchString)
    }
  }


  ngOnInit(): void {
  }

  onSubmit() {
    if (this.form.valid) {
      // Perform your submit logic here
      this.activeModal.close(this.model);
    }
  }

  dismissModal() {
    this.activeModal.dismiss('Close click');
  }


}
