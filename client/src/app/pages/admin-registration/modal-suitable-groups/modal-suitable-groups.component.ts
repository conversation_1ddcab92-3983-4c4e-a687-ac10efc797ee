import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { FormlyFieldConfig, FormlyFormOptions } from '@ngx-formly/core';
import { RegistrationService } from 'app/services/registration.service';
import Swal from 'sweetalert2';

interface Group {
  label: string;
  value: string;
}

@Component({
  selector: 'app-modal-suitable-groups',
  templateUrl: './modal-suitable-groups.component.html',
  styleUrls: ['./modal-suitable-groups.component.scss']
})
export class ModalSuitableGroupsComponent implements OnInit {
  @Input() registrations: any;
  form = new FormGroup({});
  modalRef: any;
  model = {
    group_id: '',
  }
  suitableGroups: Group[] = [];
  options: FormlyFormOptions = {};
  constructor(
    public activeModal: NgbActiveModal,
    public registrationService: RegistrationService
  ) { }

  // radio button options to choose group
  fields: FormlyFieldConfig[] = [];

  dismissModal() {
    this.activeModal.close();
  }

  submit() {
    if (this.form.valid) {
      // Perform your submit logic here
      this.activeModal.close(this.model);
    }
  }


  // get suitable groups for the selected registration (following the logic as in HKJFL)

  getSuitableGroups(registration_id: number) {
    this.registrationService.getSuitableGroups(registration_id).subscribe((response) => {

      // Assuming response is an object with suitable_groups property
      const assigned_group_ids = response.assigned_groups;

      this.suitableGroups = response.suitable_groups
        .filter((group) => !assigned_group_ids.includes(group.id))
        .map((group) => ({
          label: group.name,
          value: group.id.toString(),
        }));

      this.fields = [
        {
          key: 'group_id',
          type: 'ng-select',
          props: {
            translate: true,
            label: 'Group',
            required: true,
            closeOnSelect: true,
            options: this.suitableGroups,
            placeholder: 'Select Group',
          },
        },
      ];

      console.log(this.suitableGroups);
    },
      (err) => {
        console.log(err);
        Swal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'Something went wrong!',
        })
      }
    );

  }




  ngOnInit(): void {
    console.log(this.registrations);
    this.getSuitableGroups(this.registrations.id);
  }

}