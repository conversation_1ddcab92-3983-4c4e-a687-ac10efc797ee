import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {FormlyFieldConfig} from '@ngx-formly/core';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {TranslateService} from '@ngx-translate/core';
import {AutoScheduleService} from '../../../../services/auto-schedule.service';
import {LoadingService} from 'app/services/loading.service';
import {ToastrService} from 'ngx-toastr';
import moment from "moment/moment";

export type UpdateConfigParams = {
    tournamentId: string;
    location: string;
    date: string;
    configId: number;
    timeSlotIds: number[];
}

@Component({
    selector: 'app-modal-update-config',
    templateUrl: './modal-update-config.component.html',
    styleUrls: ['./modal-update-config.component.scss']
})
export class ModalUpdateConfigComponent implements OnInit {

    @Input() selectedConfig: UpdateConfigParams | null = null;

    editForm = new FormGroup({});
    editModel = {};

    public editFields: FormlyFieldConfig[] = [
        {
            key: 'begin_date',
            type: 'input',
            props: {
                label: this._translateService.instant('Date'),
                placeholder: this._translateService.instant('Enter begin date'),
                required: true,
                type: 'date'
            }
        },
        {
            key: 'begin_time',
            type: 'input',
            props: {
                label: this._translateService.instant('Begin time'),
                placeholder: this._translateService.instant(
                    'Enter begin time'
                ),
                required: true,
                type: 'time'
            },
            validation: {
                messages: {
                    required: this._translateService.instant('Begin time is required.')
                }
            }
        },
        {
            key: 'end_time',
            type: 'input',
            props: {
                label: this._translateService.instant('Latest End Time'),
                placeholder: this._translateService.instant('Select end time'),
                required: true,
                type: 'time'
            },
            validation: {
                messages: {
                    required: this._translateService.instant('End time is required.')
                }
            },
            validators: {
                endTimeValidator: {
                    expression: (control) => {
                        const form = control.parent;
                        if (!form) return true;
                        const beginTime = form.get('begin_time').value;
                        const endTime = control.value;
                        return !beginTime || !endTime || endTime > beginTime;
                    },
                    message: this._translateService.instant('End time must be later than start time')
                },
                matchDurationValidator: {
                    expression: (control) => {
                        const form = control.parent;
                        if (!form) return true;
                        const beginTime = form.get('begin_time').value;
                        const endTime = control.value;
                        const matchDuration = form.get('match_duration')?.value || 0;

                        if (!beginTime || !endTime) return true;

                        const start = moment(beginTime, 'HH:mm');
                        const end = moment(endTime, 'HH:mm');
                        const diffInMinutes = end.diff(start, 'minutes');

                        return diffInMinutes >= matchDuration;
                    },
                    message: this._translateService.instant('Time slot must be equal or larger than match duration')
                }
            }
        },
        {
            key: 'match_duration',
            type: 'input',
            props: {
                label: this._translateService.instant('Match duration'),
                placeholder: this._translateService.instant(
                    'Enter match duration (in minutes)'
                ),
                required: true,
                type: 'number',
                min: 1,
                max: 1440,
            },
            validation: {
                messages: {
                    required: this._translateService.instant('Match duration is required.'),
                    min: this._translateService.instant('Match duration must be at least 1 minute.'),
                    max: this._translateService.instant('Match duration must be less than 1 day (1440 minutes).')
                }
            }
        },
        {
            key: 'break_duration',
            type: 'input',
            props: {
                label: this._translateService.instant('Break duration'),
                placeholder: this._translateService.instant(
                    'Enter break between match duration (in minutes)'
                ),
                required: true,
                type: 'number',
                min: 0,
                max: 1440,
            },
            validation: {
                messages: {
                    required: this._translateService.instant('Break duration is required.'),
                    min: this._translateService.instant('Break duration must be at least 0 minutes.'),
                    max: this._translateService.instant('Break duration must be less than 1 day (1440 minutes).')
                }
            }
        },
        {
            key: 'location_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
        {
            key: 'tournament_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
        {
            key: 'config_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
    ];

    @Output() onSubmit = new EventEmitter();

    constructor(
        private _modalService: NgbModal,
        private _translateService: TranslateService,
        private _autoSchedule: AutoScheduleService,
        private _loadingService: LoadingService,
        private _toastService: ToastrService
    ) {

    }

    ngOnInit() {
        if (!this.selectedConfig) {
            console.error('No selected configuration provided');
            return;
        }


        this._loadingService.show();

        this._autoSchedule.getScheduleConfigById(this.selectedConfig.configId)
            .subscribe((res) => {
                console.log('🚀 ~ ngOnInit ~ res: ', res);
                // Convert ISO date string to 'YYYY-MM-DD' for input[type="date"]
                const beginDate = res.data.begin_date
                    ? new Date(res.data.begin_date).toISOString().slice(0, 10)
                    : '';

                this.editModel = {
                    ...this.editModel,
                    tournament_id: res.data.tournament_id,
                    location_id: res.data.location_id,
                    begin_date: beginDate,
                    begin_time: res.data.begin_time,
                    end_time: res.data.end_time,
                    config_id: res.data.id,
                    match_duration: res.data.match_duration,
                    break_duration: res.data.break_match_duration,
                    date: this.selectedConfig.date,
                    time_slot_ids: this.selectedConfig.timeSlotIds,
                };

                this._loadingService.dismiss();

            });
    }

    onSubmitEdit(model) {

        this._autoSchedule.updateScheduleConfig(model).subscribe((res) => {
            this._toastService.success(this._translateService.instant('Schedule configuration updated successfully.'));
            this.onSubmit.emit(res);
            this._modalService.dismissAll();
        }, (error) => {
            console.error('Error scheduling tournament:', error);
        });
    }


    closeModal() {
        this.editModel = {};
        this._modalService.dismissAll();
    }

    clearForm() {
        this.editForm.reset();
    }

}
