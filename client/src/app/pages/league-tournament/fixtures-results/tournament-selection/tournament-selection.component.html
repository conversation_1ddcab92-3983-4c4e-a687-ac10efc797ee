<div class="content-wrapper container-xxl p-0">
    <div class="content-body">
        <app-content-header [contentHeader]="contentHeader"></app-content-header>
        <div class="row m-0">
            <!-- ng select season -->
            <div class="col col-md-6 col-lg-3 mb-1 pl-25 pr-50">
                <!-- <label class="form-label" for="season">{{ 'Season' | translate }}</label> -->
                <ng-select [searchable]="false" [clearable]="false" placeholder="{{ 'Select Season' | translate }}"
                    [(ngModel)]="season_id" (change)="onSelectedSeasonChange($event)">
                    <ng-option *ngFor="let season of currentSeasons" [value]="season.id">{{ season.name }}
                    </ng-option>
                </ng-select>
            </div>
            <ng-container *ngIf="{ isShowFilter:true } as variable">
                <div class="col-auto col-md pl-50 pr-0 d-flex justify-content-end align-items-start">
                    <button type="button" class="btn btn-flat pl-25 pr-25"
                        (click)=" variable.isShowFilter = !variable.isShowFilter">
                        <i class="fa-light fa-filter-list fa-xl mr-25"></i>
                    </button>
                </div>

                <div class="col-12 col-md-auto p-25 mb-1" *ngIf="variable.isShowFilter">
                    <div class="row mr-0">
                        <div class="col pr-0">
                            <div style="min-width: 130px;">

                                <ng-select placeholder="{{ 'Select Group' | translate }}" [(ngModel)]="group_id"
                                    [clearable]="false" (change)="onSelectedGroupChange($event)">
                                    <ng-option [value]="''" name="all">
                                        {{ 'All' | translate }}
                                    </ng-option>
                                    <ng-option *ngFor="let group of currentGroups" [value]="group.id" name="group.id">
                                        {{ group.name }}
                                    </ng-option>
                                </ng-select>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>
        </div>
        <section id="fixtures-results">
            <table datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger" class="card_table" [id]="tableID">
                <tbody class="row ml-0 mr-0">
                </tbody>
            </table>
        </section>
    </div>
</div>

<div class="login-form">
    <!-- Modal -->
    <ng-template #modalForm let-modal>
        <div class="modal-header">
            <h4 class="modal-title" id="myModalLabel1">{{'Add fixtures to calendar' | translate}}</h4>
            <button type="button" class="close" (click)="modal.close('Cross click')" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            {{'Select Teams or Tournaments to receive fixtures and other relevant messages to your calendars.' | translate}}
        </div>
        <form [formGroup]="form" (ngSubmit)="onSubmit()">
            <div class="modal-body" tabindex="0" ngbAutofocus style="padding-bottom: 0px">
                <formly-form [form]="form" [fields]="fields" [model]="model"></formly-form>
            </div>
            <div class="modal-body" style="padding-top: 0px; padding-bottom: 0px">
                <div *ngIf="errorMessage" class="text-danger" >
                    {{ errorMessage }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" rippleEffect>
                    {{"Submit" | translate}}
                </button>
            </div>
        </form>
    </ng-template>
    <!-- / Modal -->
</div>