<div class="content-wrapper container-xxl p-0">
  <div class="content-body">
    <!-- Basic Alerts start -->
    <section id="matches-details">
      <div class="row">
        <div class="col-12">
          <div class="card p-0">
            <div class="card-body p-1">
              <div class="row">
                <div class="col d-flex align-items-center" backButton>
                  <!-- button back -->
                  <button type="button" class="btn btn-icon float-left btn-flat-dark">
                    <i class="fa fa-chevron-left" aria-hidden="true"></i>
                  </button>
                  <span class="m-0 h4">
                    {{ match_info?.stage?.tournament?.name }}
                  </span>
                </div>
                <div class="col">
                  <button [disabled]="!checkValidTimeUpdateScore()"
                          type="button" class="btn btn-outline-primary float-right btn-update"
                          (click)="openModalUpdateScore()">
                    {{ 'Update Score' | translate }}
                  </button>
                </div>
              </div>
            </div>
            <table class="table team-details">
              <tbody>
              <tr>
                <td>
                  <div class="d-flex flex-wrap flex-row-reverse align-items-center">
                    <div class="d-flex col-12 justify-content-center col-md-auto p-0">
                      <img src="{{
                            match_info?.home_team?.club?.logo
                              ? match_info?.home_team?.club?.logo
                              : coreConfig.app.appLogoImage
                          }}" alt="home team logo" class="avatar avatar-sm bg-white rounded-0" />
                    </div>
                    <div class="d-flex col-12 justify-content-center col-md-auto p-0">
                        <span class="team-name p-0">
                          {{ match_info?.home_team?.name ? match_info?.home_team?.name : 'TBD' }}
                        </span>
                    </div>

                    <!-- logo -->
                  </div>
                </td>
                <td class="text-center p-1" style="width: fit-content;min-width: 120px;">
                    <span class="h5" *ngIf="match_info?.round_name">
                      {{ match_info?.round_name }}
                    </span>
                  <br *ngIf="match_info?.round_name" />
                  <span class="badge badge-light-primary">
                      {{
                      match_info?.home_score != null
                        ? match_info?.home_score
                        : '-'
                    }}
                    </span>
                  &nbsp;-&nbsp;
                  <span class="badge badge-light-primary">
                      {{
                      match_info?.away_score != null
                        ? match_info?.away_score
                        : '-'
                    }}
                    </span>
                  <br />
                  <span class="h5" *ngIf="match_info?.home_penalty">
                      {{ match_info?.home_penalty }} -
                    {{ match_info?.away_penalty }}
                    </span>
                </td>
                <td>
                  <div class="d-flex flex-wrap flex-row align-items-center">
                    <div class="d-flex col-12 justify-content-center col-md-auto p-0">
                      <img src="{{
                            match_info?.away_team?.club?.logo
                              ? match_info?.away_team?.club?.logo
                              : coreConfig.app.appLogoImage
                          }}" alt="away team logo" class="avatar avatar-sm bg-white rounded-0" />
                    </div>
                    <div class="d-flex col-12 justify-content-center col-md-auto p-0">
                        <span class="team-name p-0">
                          {{ match_info?.away_team?.name ? match_info?.away_team?.name : 'TBD' }}
                        </span>
                    </div>
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
            <hr />
            <div>
              <div class="row mb-1" *ngIf="initSettings.sport_type==='football'">
                <div class="text-center col">
                  <button type="button" class="btn btn-outline-warning btn-update"
                          (click)="openModalUpdataEvent(match_info?.home_team?.id)">
                    {{ 'Add Event' | translate }}
                  </button>
                </div>
                <div class="text-center col">
                  <button type="button" class="btn btn-outline-warning btn-update"
                          (click)="openModalUpdataEvent(match_info?.away_team?.id)">
                    {{ 'Add Event' | translate }}
                  </button>
                </div>
              </div>

              <div class="row">
                <div class="text-center col">
                  <div class="m-25" *ngFor="let item of home_team_details">
                    <button type="button" class="btn btn-icon btn-icon rounded-circle btn-flat-danger mr-25"
                            rippleEffect (click)="removeEvent(item)">
                      <i class="fa-solid fa-trash-can-xmark"></i>
                    </button>
                    <span>
                      <i [class]="getIconByType(item.type)"></i>
                    </span>
                    {{ name_settings?.is_on == 1 
                      ? item?.player?.user?.first_name + ' ' + item?.player?.user?.last_name 
                      : item?.player?.user?.last_name + ' ' + item?.player?.user?.first_name }}
                    <span class="text-secondary">{{ item?.time }}'</span>
                    <br />
                    <span class="text-muted" *ngIf="item?.note">
                      {{ 'Note' | translate }}: {{ item?.note }}
                    </span>
                  </div>
                </div>
                <div class="text-center col">
                  <div class="m-25" *ngFor="let item of away_team_details">
                    <button type="button" class="btn btn-icon btn-icon rounded-circle btn-flat-danger mr-25"
                            rippleEffect (click)="removeEvent(item)">
                      <i class="fa-solid fa-trash-can-xmark"></i>
                    </button>
                    <span>
                      <i [class]="getIconByType(item.type)"></i>
                    </span>
                    {{ name_settings?.is_on == 1 
                      ? item?.player?.user?.first_name + ' ' + item?.player?.user?.last_name 
                      : item?.player?.user?.last_name + ' ' + item?.player?.user?.first_name }}
                    <span class="text-secondary">{{ item?.time }}'</span>
                    <br />
                    <span class="text-muted" *ngIf="item?.note">
                      {{ 'Note' | translate }}: {{ item?.note }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Basic Alerts end -->
  </div>
</div>