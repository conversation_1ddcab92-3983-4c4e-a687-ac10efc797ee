import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LeagueComponent } from './league/league.component';
import { LeagueReportsComponent } from './league-reports/league-reports.component';
import { RouterModule, Routes } from '@angular/router';
import { NgbAccordionModule, NgbCollapseModule, NgbDropdownModule, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ContentHeaderModule } from 'app/layout/components/content-header/content-header.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ErrorMessageModule } from 'app/layout/components/error-message/error-message.module';
import { TranslateModule } from '@ngx-translate/core';
import { CoreSidebarModule } from '@core/components';
import { CoreCommonModule } from '@core/common.module';
import { DataTablesModule } from 'angular-datatables';
import { NgSelectModule } from '@ng-select/ng-select';
import { EditorSidebarModule, serverValidationMessage } from 'app/components/editor-sidebar/editor-sidebar.module';
import { BtnDropdownActionModule } from 'app/components/btn-dropdown-action/btn-dropdown-action.module';
import { StagesComponent } from './stages/stages.component';
import { StageDetailsComponent } from './stages/stage-details/stage-details.component';
import { FormlyModule } from '@ngx-formly/core';
import { FormlyBootstrapModule } from '@ngx-formly/bootstrap';
import { NumberTypeComponent } from 'app/components/number-type/number-type.component';
import { CoreTouchspinModule } from '@core/components/core-touchspin/core-touchspin.module';
import { DetailsWrapperComponent } from './stages/stage-details/details-wraper.component';
import { StageTablesComponent } from './stages/stage-tables/stage-tables.component';
import { StageTeamsComponent } from './stages/stage-teams/stage-teams.component';
import { ModalAddGroupTeamComponent } from './stages/stage-teams/modal-add-group-team/modal-add-group-team.component';
import { StageMatchesComponent } from './stages/stage-matches/stage-matches.component';
import { LeagueMatchesComponent } from './league-matches/league-matches.component';
import { ScrollableTabsModule } from 'app/components/scrollable-tabs/scrollable-tabs.module';
import { UpadateMatchesComponent } from './league-matches/update-matches/update-matches.component';
import { ModalUpdateScoreComponent } from './modal-update-score/modal-update-score.component';
import { UpdateMatchDetailsComponent } from './modal-update-match-details/update-match-details.component';
import { FixturesResultsComponent } from './fixtures-results/fixtures-results.component';
import { FixturesComponent } from './fixtures-results/fixtures/fixtures.component';
import { MatchCardModule } from 'app/components/match-card/match-card.module';
import { MatchesDetailsComponent } from './fixtures-results/matches-details/matches-details.component';
import { MatchDetailsGuard } from 'app/guards/match-details.guard';
import { HostListenersModule } from 'app/hostlisteners/host-listeners.module';
import { PermissionsGuard } from 'app/guards/permissions.guard';
import { AppConfig } from 'app/app-config';
import { RowMatchModule } from 'app/components/row-match/row-match.module';
import { TournamentSelectionComponent } from './fixtures-results/tournament-selection/tournament-selection.component';
import { TeamFixturesComponent } from './fixtures-results/team-fixtures/team-fixtures.component';
import { CorePipesModule } from '@core/pipes/pipes.module';
import { ModalManageRefereesComponent } from './league/modal-manage-referees/modal-manage-referees.component';
import {
  ModalAssignRefereesComponent
} from './stages/stage-matches/modal-assign-referees/modal-assign-referees.component';
import { AutoScheduleComponent } from './auto-schedule/auto-schedule.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { ModalSetupScheduleComponent } from './auto-schedule/modal-setup-schedule/modal-setup-schedule.component';
import { ModalUpdateConfigComponent } from './auto-schedule/modal-update-config/modal-update-config.component';
import { ModalCrudBreakComponent } from './auto-schedule/modal-crud-break/modal-crud-break.component';
import { ModalUpdateMatchComponent } from './auto-schedule/modal-update-match/modal-update-match.component';


const routes: Routes = [
  {
    path: 'manage',
    component: LeagueComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.manage_leagues }
  },
  {
    path: 'manage/:tournament_id/auto-schedule',
    component: AutoScheduleComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.manage_leagues }
  },
  {
    path: 'manage/:tournament_id/stages/:stage_id',
    component: StagesComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.manage_leagues }
  },
  {
    path: 'reports',
    component: LeagueReportsComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.league_reports }
  },
  {
    path: 'matches',
    component: LeagueMatchesComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.update_score }
  },
  {
    path: 'matches/:match_id/update-details',
    component: UpadateMatchesComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.update_score }
  },
  {
    path: 'matches/:match_id/details',
    component: MatchesDetailsComponent,
    canActivate: [MatchDetailsGuard]
  },
  {
    path: 'fixtures-results/:tournament_id',
    component: FixturesResultsComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.fixtures_results }
  },
  {
    path: 'fixtures-results/team/:team_id',
    component: TeamFixturesComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.fixtures_results }
  },
  {
    path: 'select-tournament',
    component: TournamentSelectionComponent,
    canActivate: [PermissionsGuard],
    data: { permissions: AppConfig.PERMISSIONS.fixtures_results }
  }
];

@NgModule({
  declarations: [
    LeagueComponent,
    StagesComponent,
    LeagueReportsComponent,
    StageDetailsComponent,
    NumberTypeComponent,
    DetailsWrapperComponent,
    StageTablesComponent,
    StageTeamsComponent,
    ModalAddGroupTeamComponent,
    StageMatchesComponent,
    LeagueMatchesComponent,
    UpadateMatchesComponent,
    MatchesDetailsComponent,
    ModalUpdateScoreComponent,
    UpdateMatchDetailsComponent,
    FixturesResultsComponent,
    FixturesComponent,
    TournamentSelectionComponent,
    TeamFixturesComponent,
    ModalManageRefereesComponent,
    ModalAssignRefereesComponent,
    AutoScheduleComponent,
    ModalSetupScheduleComponent,
    ModalUpdateConfigComponent,
    ModalCrudBreakComponent,
    ModalUpdateMatchComponent
  ],
  imports: [
    HostListenersModule,
    CoreCommonModule,
    NgbAccordionModule,
    NgbModule,
    NgbDropdownModule,
    CommonModule,
    RouterModule.forChild(routes),
    ContentHeaderModule,
    FormsModule,
    ReactiveFormsModule,
    ErrorMessageModule,
    TranslateModule,
    CoreSidebarModule,
    CoreCommonModule,
    CorePipesModule,
    DataTablesModule,
    NgSelectModule,
    BtnDropdownActionModule,
    CoreTouchspinModule,
    ScrollableTabsModule,
    NgbCollapseModule,
    FormlyBootstrapModule,
    MatchCardModule,
    RowMatchModule,
    EditorSidebarModule,
    DragDropModule,
    FormlyModule.forRoot({
      validationMessages: [
        { name: 'serverError', message: serverValidationMessage }
      ],
      wrappers: [{ name: 'details', component: DetailsWrapperComponent }]
    })
  ],
  exports: [
    LeagueComponent,
    StagesComponent,
    LeagueReportsComponent,
    NumberTypeComponent,
    ModalUpdateScoreComponent,
    UpdateMatchDetailsComponent
  ]
})
export class LeagueTournamentModule {
}
