<div class="content-wrapper container-xxl p-0">
  <div class="content-body">
    <div class="card">
      <div class="card-header px-0 pt-0">
        <ul
          ngbNav
          #nav="ngbNav"
          class="nav-tabs"
          *ngIf="stage.type === AppConfig.TOURNAMENT_TYPES.league"
        >
          <li ngbNavItem>
            <a ngbNavLink (click)="filterFriendlyMatches(false)">{{
                'All Matches' | translate
              }}</a>
          </li>
          <li ngbNavItem>
            <a ngbNavLink (click)="filterFriendlyMatches(true)">{{
                'Friendly Matches' | translate
              }}</a>
          </li>
        </ul>
      </div>

      <table
        datatable
        [dtOptions]="dtOptions"
        [dtTrigger]="dtTrigger"
        class="table row-border hover"
      ></table>
    </div>
  </div>
</div>

<core-sidebar
  class="modal modal-slide-in sidebar-todo-modal fade"
  [name]="table_name"
  overlayClass="modal-backdrop"
>
  <app-editor-sidebar
    [table]="dtElement"
    [fields]="fields"
    [params]="params"
    (onSuccess)="onSuccess($event)"
    (onClose)="onCloseSidebar($event)"
    [paramsToPost]="paramsToPost"
    [fields_subject]="fields_subject"
  >
  </app-editor-sidebar>
</core-sidebar>

<ng-template #modalAssignReferee let-modal>
  <app-modal-assign-referees
    [isMultipleAssign]="isMultipleAssign"
    [selectedIds]="selectedIds"
    [listReferees]="listReferees"
    [assignRefereeForm]="assignRefereeForm"
    [assignRefereeFields]="assignRefereeFields"
    [assignRefereeModel]="assignRefereeModel"
    (onSubmit)="onSubmitAssignReferee($event)"
  />
</ng-template>
