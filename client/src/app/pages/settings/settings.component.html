<section class="vertical-wizard">
  <app-content-header [contentHeader]="contentHeader"></app-content-header>
  <div
    id="stepper2"
    class="bs-stepper vertical vertical-wizard-example"
    *ngIf="settings"
  >
    <div class="bs-stepper-header">
      <div class="step" data-target="#system-versions">
        <button type="button" class="step-trigger"
                (click)="onChangeStep(1)">
          <span class="bs-stepper-box"
          ><i class="fa-solid fa-layer-group"></i
          ></span>
          <span class="bs-stepper-label">
            <span class="bs-stepper-title">{{
                'System Versions' | translate
              }}</span>
            <span class="bs-stepper-subtitle">{{
                'iOS & Android version settings' | translate
              }}</span>
          </span>
        </button>
      </div>
      <div class="step" data-target="#smtp-account">
        <button type="button" class="step-trigger" (click)="onChangeStep(2)">
          <span class="bs-stepper-box"
          ><i class="fa-regular fa-envelopes-bulk"></i
          ></span>
          <span class="bs-stepper-label">
            <span class="bs-stepper-title"
            >SMTP {{ 'Account' | translate }}</span
            >
            <span class="bs-stepper-subtitle">{{
                'Setup SMTP Account' | translate
              }}</span>
          </span>
        </button>
      </div>
      <div class="step" data-target="#notification">
        <button type="button" class="step-trigger" (click)="onChangeStep(3)">
          <span class="bs-stepper-box"><i class="fa-regular fa-bell"></i></span>
          <span class="bs-stepper-label">
            <span class="bs-stepper-title">{{
                'App Start Notification' | translate
              }}</span>
            <span class="bs-stepper-subtitle">{{
                'Setting will show when app start' | translate
              }}</span>
          </span>
        </button>
      </div>
      <!-- <div class="step" data-target="#policy_notification">
                <button type="button" class="step-trigger">
                    <span class="bs-stepper-box"><i class="bi bi-send-plus-fill"></i></span>
                    <span class="bs-stepper-label">
                        <span class="bs-stepper-title">{{'PICS'|translate}}</span>
                        <span class="bs-stepper-subtitle">{{'Terms of Service and Privacy Policy'|translate}}</span>
                    </span>
                </button>
            </div> -->

      <div class="step" data-target="#livekit">
        <button type="button" class="step-trigger" (click)="onChangeStep(4)">
          <span class="bs-stepper-box"
          ><i class="fa-regular fa-video"></i
          ></span>
          <span class="bs-stepper-label">
            <span class="bs-stepper-title"
            >LiveKit {{ 'Account' | translate }}</span
            >
            <span class="bs-stepper-subtitle">{{
                'Setup LiveKit Account' | translate
              }}</span>
          </span>
        </button>
      </div>
      <div
        class="step"
        data-target="#payment_settings"
        *ngIf="settings && settings[2]?.value?.is_payment_required"
      >
        <button type="button" class="step-trigger" (click)="onChangeStep(5)">
          <span class="bs-stepper-box"
          ><i class="fa-regular fa-credit-card"></i
          ></span>
          <span class="bs-stepper-label">
            <span class="bs-stepper-title">{{
                'Payment Settings' | translate
              }}</span>
            <span class="bs-stepper-subtitle">{{
                'Setup Payment' | translate
              }}</span>
          </span>
        </button>
      </div>
      <div class="step" data-target="#scoreboard">
        <button type="button" class="step-trigger" (click)="onChangeStep(6)">
          <span class="bs-stepper-box"><i class="bi bi-terminal"></i></span>
          <span class="bs-stepper-label">
            <span class="bs-stepper-title">{{ 'Scoreboard' | translate }}</span>
            <span class="bs-stepper-subtitle">{{
                'Select group - league will show in scoreboard' | translate
              }}</span>
          </span>
        </button>
      </div>
      <div class="step" data-target="#name_settings">
        <button type="button" class="step-trigger" (click)="onChangeStep(7)">
          <span class="bs-stepper-box"><i class="bi bi-person-lines-fill"></i></span>
          <span class="bs-stepper-label">
            <span class="bs-stepper-title">{{ 'Name Configuration Settings' | translate }}</span>
            <span class="bs-stepper-subtitle">{{
                'Configure name display settings for different regions.' | translate
              }}</span>
          </span>
        </button>
      </div>
      <!-- <div class="step" data-target="#check-update">
                <button type="button" class="step-trigger">
                    <span class="bs-stepper-box"><i class="fa-solid fa-rotate"></i></span>
                    <span class="bs-stepper-label">
                        <span class="bs-stepper-title">{{'Update'|translate }}</span>
                        <span class="bs-stepper-subtitle">{{'Check update version'|translate}}</span>
                    </span>
                </button>
            </div> -->
    </div>
    <div class="bs-stepper-content">
      <div id="system-versions" class="content">
        <div class="content-header">
          <h5 class="mb-0">{{ 'System Versions' | translate }}</h5>
          <small>{{ 'iOS & Android version settings' | translate }}</small>
        </div>
        <form
          [formGroup]="version_form"
          (ngSubmit)="
            saveSetting(
              AppConfig.SETTINGS_KEYS.REQUIRED_VERSIONS,
              version_model,
              version_form
            )
          "
        >
          <formly-form
            [model]="version_model"
            [fields]="version_fields"
            [options]="version_options"
            [form]="version_form"
          ></formly-form>

          <div class="d-flex justify-content-between">
            <button type="submit" class="btn btn-primary btn-next" rippleEffect>
              <span class="align-middle">{{ 'Save' | translate }}</span>
            </button>
          </div>
        </form>
      </div>
      <div id="smtp-account" class="content">
        <div class="content-header">
          <h5 class="mb-0">SMTP {{ 'Account' | translate }}</h5>
          <small class="text-muted"
          >{{ 'Enter Your SMTP Account' | translate }}.</small
          >
        </div>
        <form
          [formGroup]="smtp_form"
          (ngSubmit)="
            saveSetting(AppConfig.SETTINGS_KEYS.SMTP, smtp_model, smtp_form)
          "
        >
          <formly-form
            [model]="smtp_model"
            [fields]="smtp_fields"
            [options]="smtp_options"
            [form]="smtp_form"
          ></formly-form>

          <div class="d-flex justify-content-between">
            <button type="submit" class="btn btn-primary btn-next" rippleEffect>
              <span class="align-middle">{{ 'Save' | translate }}</span>
            </button>
          </div>
        </form>
      </div>

      <div id="notification" class="content">
        <div class="content-header">
          <h5 class="mb-0">{{ 'Notification' | translate }}</h5>
          <small>{{ 'Notification settings' | translate }}</small>
        </div>
        <form
          [formGroup]="notification_form"
          (ngSubmit)="
            saveSetting(
              AppConfig.SETTINGS_KEYS.NOTIFICATION,
              notification_model,
              notification_form
            )
          "
        >
          <formly-form
            [model]="notification_model"
            [fields]="notification_fields"
            [options]="notification_options"
            [form]="notification_form"
          ></formly-form>

          <div class="d-flex justify-content-between">
            <button type="submit" class="btn btn-primary btn-next" rippleEffect>
              <span class="align-middle">{{ 'Save' | translate }}</span>
            </button>
          </div>
        </form>
      </div>
      <!-- <div id="policy_notification" class="content">
                <div class="content-header">
                    <h5 class="mb-0">{{'Terms & Conditions and Privacy Information Collection Statement'|translate}}</h5>
                    <small>{{'Terms of Service and Privacy Policy settings'|translate}}</small>
                </div>
                <form [formGroup]="policy_notification_form" style="width: 99%;"
                    (ngSubmit)="saveSetting(AppConfig.SETTINGS_KEYS.POLICY_NOTIFICATION,policy_notification_model,policy_notification_form)">
                    <formly-form [model]="policy_notification_model" style="" [fields]="policy_notification_fields"
                        [options]="policy_notification_options" [form]="policy_notification_form"></formly-form>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary btn-next" rippleEffect>
                            <span class="align-middle">{{'Save'|translate}}</span>
                        </button>
                    </div>
                </form>
            </div> -->

      <div id="payment_settings" class="content" *ngIf="settings && settings[2]?.value?.is_payment_required">
        <div class="content-header">
          <h5 class="mb-0">{{ 'Payment Settings' | translate }}</h5>
          <small class="text-muted"
          >{{ 'Enter Your Payment Config' | translate }}.</small
          >
        </div>
        <form
          [formGroup]="payment_form"
          (ngSubmit)="
            saveSetting(
              AppConfig.SETTINGS_KEYS.PAYMENT,
              payment_model,
              payment_form
            )
          "
        >
          <formly-form
            [model]="payment_model"
            [fields]="payment_fields"
            [options]="payment_options"
            [form]="payment_form"
          ></formly-form>

          <div class="d-flex justify-content-between">
            <button type="submit" class="btn btn-primary btn-next" rippleEffect>
              <span class="align-middle">{{ 'Save' | translate }}</span>
            </button>
          </div>
        </form>
      </div>

      <div id="livekit" class="content">
        <div class="content-header">
          <h5 class="mb-0">{{ 'LiveKit Settings' | translate }}</h5>
          <small class="text-muted"
          >{{ 'Enter Your LiveKit Config' | translate }}.</small
          >
          <!-- tutorial link -->
          <small class="text-muted">
            <a href="https://cloud.livekit.io" target="_blank">
              {{ 'Get token from here' | translate }}
            </a>
          </small>
        </div>
        <form
          [formGroup]="livekit_form"
          (ngSubmit)="
            saveSetting(
              AppConfig.SETTINGS_KEYS.LIVEKIT,
              livekit_model,
              livekit_form
            )
          "
        >
          <formly-form
            [model]="livekit_model"
            [fields]="livekit_fields"
            [options]="livekit_options"
            [form]="livekit_form"
          ></formly-form>

          <div class="d-flex justify-content-between">
            <button type="submit" class="btn btn-primary btn-next" rippleEffect>
              <span class="align-middle">{{ 'Save' | translate }}</span>
            </button>
          </div>
        </form>
      </div>

      <div id="name_settings" class="content">
        <div class="content-header">
          <h5 class="mb-0">{{ 'Name Configuration Settings' | translate }}</h5>
          <small class="text-muted">
            {{ 'Configure name display settings for different regions.' | translate }}.
          </small>
        </div>
        <form
            [formGroup]="name_form"
            (ngSubmit)="
              saveSetting(
                AppConfig.SETTINGS_KEYS.NAME_SETTINGS,
                name_model,
                name_form
              )
            "
          >
            <formly-form
              [model]="name_model"
              [fields]="name_fields"
              [options]="name_options"
              [form]="name_form"
            ></formly-form>

            <div class="d-flex justify-content-between">
              <button type="submit" class="btn btn-primary btn-next" rippleEffect>
                <span class="align-middle">{{ 'Save' | translate }}</span>
              </button>
            </div>
          </form>
      </div>

      <div id="scoreboard" class="content">
        <div class="content-header">
          <h5 class="mb-0">{{ 'Scoreboard' | translate }}</h5>
          <small class="text-secondary">{{
              'Select group - league will show in scoreboard' | translate
            }}</small>
        </div>
        <div class="content-body">
          <div class="col px-0">
            <label for="season">{{ 'Season' | translate }}</label>
            <ng-select
              [searchable]="true"
              [clearable]="false"
              placeholder="{{ 'Select Season' | translate }}"
              [(ngModel)]="season"
              (change)="onChangeSeason($event)"
            >
              <ng-option
                class="text-secondary"
                *ngFor="let season of seasons"
                [value]="season"
              >{{ season.name | translate }}
              </ng-option>
            </ng-select>
          </div>
          <div class="mt-2">
            <label class="mb-0">{{ 'Select Tournament' | translate }}</label>
            <br />
            <small class="text-secondary">{{
                'You can drag tournament to arrange display order.' | translate
              }}</small>
            <div cdkDropListGroup>
              <div
                cdkDropList
                [cdkDropListData]="tournaments"
                (cdkDropListDropped)="drop($event)"
                class="tournament-list"
              >
                <div
                  *ngFor="let tournament of tournaments"
                  cdkDrag
                  class="card mt-1 mb-1"
                  style="
                    cursor: move;
                    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
                  "
                >
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-4 p-0">
                        <div class="form-check form-check-inline mr-0 pl-1">
                          <i class="bi bi-list mr-1"></i>
                          <label
                            class="form-check-label"
                            [for]="'checkbox' + tournament.id"
                          >{{ tournament.name | translate }}</label
                          >
                        </div>
                      </div>
                      <div class="col-md-8">
                        <div class="row">
                          <div
                            class="col-4 px-0"
                            *ngFor="let stage of tournament.stages"
                          >
                            <div class="form-check form-check-inline mx-1">
                              <input
                                class="form-check-input"
                                type="checkbox"
                                [id]="'checkbox' + stage.id"
                                [value]="stage.show_on_scoreboard"
                                [checked]="stage.show_on_scoreboard === 1"
                                (change)="
                                  updateShowOnScoreboard(
                                    tournament.id,
                                    stage.id,
                                    $event.target.checked
                                  )
                                "
                              />
                              <label
                                class="form-check-label"
                                [for]="'checkbox' + stage.id"
                              >{{ stage.name | translate }}</label
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="d-flex justify-content-between">
          <button
            type="submit"
            class="btn btn-primary btn-next"
            (click)="submit()"
            rippleEffect
          >
            <span class="align-middle">{{ 'Save' | translate }}</span>
          </button>
        </div>
      </div>
      <!-- <div id="check-update" class="content">
                <app-check-update></app-check-update>
            </div> -->
    </div>
  </div>
</section>
