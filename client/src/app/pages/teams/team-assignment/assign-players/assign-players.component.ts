import { HttpClient } from '@angular/common/http';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Renderer2,
  SimpleChanges,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { CommonsService } from 'app/services/commons.service';
import { environment } from 'environments/environment';
import Swal from 'sweetalert2';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DataTableDirective } from 'angular-datatables';
import { LoadingService } from 'app/services/loading.service';
import { TeamService } from 'app/services/team.service';
import {
  AvailablePlayerModalComponent
} from '../assign-players/available-player-modal/available-player-modal.component';
import { ToastrService } from 'ngx-toastr';
import { ExportService } from 'app/services/export.service';

@Component({
  selector: 'app-assign-players',
  templateUrl: './assign-players.component.html',
  styleUrls: ['./assign-players.component.scss']
})
export class TeamPlayersComponent implements AfterViewInit, OnInit, OnChanges {

  @Input() team: any;

  onSubmitted: EventEmitter<any> = new EventEmitter();
  public teamId: any;
  private unlistener: () => void;

  public availablePlayers = AvailablePlayerModalComponent;

  public seasonId: any;
  public clubId: any;
  public groupId: any;
  @ViewChild('modalValidator') modalValidator: TemplateRef<any>;
  dtOptions: any = {};
  dtTrigger: any = {};
  public modalRef: any;
  @ViewChild(DataTableDirective, { static: false })
  dtElement: any = DataTableDirective;
  isProcessing: boolean = false;

  params = {
    season_id: null,
    club_id: null,
    group_id: null
  };
  team_player_url = '';

  constructor(
    private route: ActivatedRoute,
    public _router: Router,
    public _commonsService: CommonsService,
    public _http: HttpClient,
    public _translateService: TranslateService,
    public renderer: Renderer2,
    public _teamService: TeamService,
    public _modalService: NgbModal,
    public _loadingService: LoadingService,
    public _toastService: ToastrService,
    private _exportService: ExportService
  ) {
    const teamManagement = localStorage.getItem('teamManagement');
    const season_id = teamManagement
      ? JSON.parse(teamManagement).seasonSelected
      : null;
    this.seasonId = season_id;
    this.teamId = this.route.snapshot.paramMap.get('teamId');
    this.team_player_url = `${environment.apiUrl}/teams/${this.teamId}/players`;
    this.route.queryParams.subscribe((params) => {
      this.clubId = params['clubId'];
    });
  }

  ngOnInit(): void {
    $.fx.off = true; //this is for disable jquery animation


    // this.dtOptions[0] = this.buildDtOptions1(current_season_player_url, params, buttons_assign);
    this.dtOptions = this.buildDtOptions(this.team_player_url, this.params);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['team']) {
      if (this.team) {
        let current_season_player_url = `${environment.apiUrl}/registrations/club-group-approved`;
        this.params = {
          season_id: this.team.group.season_id,
          club_id: this.team.club_id,
          group_id: this.team.group_id
        };
        this.dtOptions = this.buildDtOptions(this.team_player_url, this.params);
      }
    }
  }

  buildDtOptions(url, params: any, buttons?: any[]) {
    return {
      dom: this._commonsService.dataTableDefaults.dom,
      ajax: (dataTablesParameters: any, callback) => {
        if (params) {
          dataTablesParameters['season_id'] = this.seasonId;
          dataTablesParameters['club_id'] = parseInt(params.club_id);
          dataTablesParameters['group_id'] = parseInt(params.group_id);
        }
        this._http
          .post<any>(`${url}`, dataTablesParameters)
          .subscribe((resp: any) => {
            callback({
              // this function callback is used to return data to datatable
              recordsTotal: resp.recordsTotal,
              recordsFiltered: resp.recordsFiltered,
              data: resp.data
            });
          });
      },

      select: {
        toggleable: false
      },
      // serverSide: true,
      rowId: 'id',
      // fake data
      responsive: true,
      scrollX: false,
      language: this._commonsService.dataTableDefaults.lang,
      lengthMenu: this._commonsService.dataTableDefaults.lengthMenu,
      displayLength: -1,
      columnDefs: [
        { responsivePriority: 1, targets: -1 },
        { responsivePriority: 2, targets: 0 },
        { responsivePriority: 3, targets: 1 }
      ],
      columns: [
        {
          //photo
          data: 'player.photo',
          render: (data, type, row) => {
            if (data) {
              return `<img src="${data}" width="50px" height="70px" />`;
            } else {
              return `<img src="assets/images/avatars/default.png" width="50px" height="70px" />`;
            }
          }
        },
        {
          // name
          data: null,
          className: 'font-weight-bolder',
          render: (data, type, row) => {
            row = row.player.user;
            const name_settings = JSON.parse(localStorage.getItem('name_settings'));
            if (row.first_name && row.last_name) {
              if (name_settings && name_settings.is_on == 1) {
                return row.first_name + ' ' + row.last_name;
              } else {
                return row.last_name + ' ' + row.first_name;
              }
            } else {
              return '';
            }
          }
        },
        {
          //year
          data: 'player.dob',
          render: (data, type, row) => {
            return new Date(data).getFullYear();
          }
        },
        {
          //gender
          data: 'player.gender',
          render: (data, type, row) => {
            return data == 'Male'
              ? this._translateService.instant('Male')
              : this._translateService.instant('Female');
          }
        },

        {
          data: 'player.id',
          render: (data, type, row) => {
            return (
              `<button class="btn btn-outline-danger btn-sm" 
            data-row = '${JSON.stringify(row)}'
            action="remove">` +
              '<i class="fa-solid fa-xmark"></i>&nbsp;' +
              this._translateService.instant('Remove') +
              `</button>`
            );
          }
        }
      ],
      buttons: {
        dom: this._commonsService.dataTableDefaults.buttons.dom,
        buttons: [
          {
            text:
              '<i class="fa fa-user-plus"></i> ' +
              this._translateService.instant('Assign Player'),
            titleAttr: this._translateService.instant('Assign Player'),
            action: () => this.assignPlayerToTeam()
          },
          {
            text:
              '<i class="fa fa-check"></i> ' +
              this._translateService.instant('Submit Teamsheet'),
            titleAttr: this._translateService.instant('Submit Teamsheet'),
            action: () => this.submitTeamSheet()
          },
          {
            extend: 'excel',
            text: `<i class="fa-regular fa-file-excel"></i> ${this._translateService.instant(
              'Export'
            )}`,
            className: 'float-right mr-2',
            action: async (e: any, dt: any, button: any, config: any) => {
              const data = dt.buttons.exportData();
              await this._exportService.exportExcel(data, 'TeamPlayer.xlsx');
            }
          }
        ]
      }
    };
  }

  assignPlayerToTeam() {
    // open modal
    const modalRef = this._modalService.open(AvailablePlayerModalComponent, {
      size: 'lg',
      backdrop: 'static',
      centered: true,
      keyboard: true
    });

    modalRef.componentInstance.params = {
      team_id: this.teamId,
      season_id: this.params.season_id,
      club_id: this.params.club_id,
      group_id: this.params.group_id
    };

    modalRef.result.then(
      (result) => {
        // reload datatable
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
          dtInstance.ajax.reload();
        });
      },
      (reason) => {
        // reload datatable
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
          dtInstance.ajax.reload();
        });
      }
    );
  }

  submitTeamSheet() {
    // this.onSubmitted.emit();
    Swal.fire({
      title: this._translateService.instant('Are you sure?'),
      html:
        `<div class="text-center">
              <img src="assets/images/alerts/r_u_sure.svg" width="200px" height="149px">
              <p class="text-center">` +
        this._translateService.instant('ask_want_to_submit_teamsheet') +
        `</p>
            </div>`,
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonText: this._translateService.instant('Yes'),
      confirmButtonColor: '#3085d6',
      cancelButtonText:
        '<span class="text-primary">' +
        this._translateService.instant('No') +
        '</span>',
      cancelButtonColor: '#d33',
      buttonsStyling: false,
      customClass: {
        confirmButton: 'btn btn-primary mr-1',
        cancelButton: 'btn btn-outline-primary mr-1'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        console.log('submit team sheet');
        this._loadingService.show();
        this._teamService.submitTeamSheet(this.teamId).subscribe(
          (resp) => {
            this._loadingService.dismiss();
            // reload datatable
            this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
              dtInstance.ajax.reload();
            });
            console.log('resp', resp);
            this._toastService.success(
              this._translateService.instant(
                'Team sheet submitted successfully'
              )
            );
          },
          (err) => {
            this._loadingService.dismiss();
            Swal.fire({
              title: 'Warning!',
              icon: 'warning',
              text: err.message,
              confirmButtonText: this._translateService.instant('OK')
            });
          }
        );
      }
    });
  }

  editor(action, row, target = null) {
    this.isProcessing = true;
    switch (action) {
      case 'remove':
        Swal.fire({
          title: this._translateService.instant('Are you sure?'),
          html:
            `
        <div class="text-center">
          <img src="assets/images/alerts/are_you_sure.svg" width="200px" height="149px">
          <p class="text-center">` +
            this._translateService.instant('ask_want_to_remove_player') +
            `
          </p>
        </div>`,
          reverseButtons: true,
          allowOutsideClick: false,
          allowEscapeKey: false,
          confirmButtonText: this._translateService.instant('Yes'),
          showCancelButton: true,
          confirmButtonColor: '#d33',
          cancelButtonColor: '#3085d6',
          // cancel buton text color
          cancelButtonText: this._translateService.instant('Cancel'),
          buttonsStyling: false,
          customClass: {
            confirmButton: 'btn btn-primary mr-1',
            cancelButton: 'btn btn-outline-primary mr-1'
          }
        }).then((result) => {
          if (result.isConfirmed) {
            let row_id = row.id;
            let player_id = row.player.id;
            let params: FormData = new FormData();
            const teamManagement = localStorage.getItem('teamManagement');
            const season_id = teamManagement
              ? JSON.parse(teamManagement).seasonSelected
              : null;
            params.append('action', 'remove');
            params.append('data[' + row_id + '][team_id]', this.teamId);
            params.append('data[' + row_id + '][player_id]', player_id);
            params.append('data[' + row_id + '][season_id]', season_id);

            this._teamService.editorTableTeamPlayers(params).subscribe(
              (resp: any) => {
                if (resp) {
                  this._toastService.success(
                    this._translateService.instant(
                      'Player removed successfully'
                    )
                  );
                }

                // reload
                this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
                  dtInstance.ajax.reload();
                  setTimeout(() => {
                    this.toggleRemoveButton(target, false);
                  }, 1000);
                });
              },
              (err) => {
                this.toggleRemoveButton(target, false);
                let message = '';
                if (err.hasOwnProperty('error')) {
                  if (typeof err.error === 'string') {
                    message = err.error;
                  } else {
                    message = err.error[0];
                  }
                } else {
                  message = err.message;
                }
                Swal.fire({
                  title: 'Warning!',
                  icon: 'warning',
                  html: message,
                  confirmButtonText: this._translateService.instant('OK')
                });
              }
            );
          } else {
            this.toggleRemoveButton(target, false);
          }
        });
        break;

      default:
        break;
    }
  }

  toggleRemoveButton(target, action) {
    if (action) {
      this.isProcessing = true;
      target.setAttribute('disabled', 'disabled');
    } else {
      this.isProcessing = false;
      target.removeAttribute('disabled');
    }
  }

  ngAfterViewInit(): void {
    this.unlistener = this.renderer.listen('document', 'click', (event) => {
      if (event.target.hasAttribute('data-row')) {
        let target = event.target;
        let row = target.getAttribute('data-row');
        row = JSON.parse(row);
        this.editor(target.getAttribute('action'), row, target);
        // disable remove button
        target.setAttribute('disabled', 'disabled');
      }
    });
  }
}