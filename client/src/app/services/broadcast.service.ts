import { Injectable } from '@angular/core';
import { inject } from '@angular/core';
import {
  Firestore,
  collectionData,
  collection,
  addDoc,
  setDoc,
  getDoc,
  getDocs,
  doc,
  deleteDoc,
  updateDoc,
  CollectionReference,
  docSnapshots,
} from '@angular/fire/firestore';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class BroadcastService {
  live_matches$: Observable<any[]>;
  firestore: Firestore = inject(Firestore);
  liveMatchesCollection: CollectionReference;
  constructor() {
    this.liveMatchesCollection = collection(this.firestore, 'live_matches');
    this.live_matches$ = collectionData(this.liveMatchesCollection);
  }

  // add match to live matches
  async addLiveMatch(id, match: any) {
    return setDoc(doc(this.liveMatchesCollection, id), match);
  }

  // add event to live match
  addLiveMatchEvent(id, events: any) {
    return setDoc(
      doc(this.liveMatchesCollection, id),
      {
        events: events,
      },
      {
        merge: true,
      }
    );
  }

  subscribeToLiveMatch(id) {
    return docSnapshots(doc(this.liveMatchesCollection, id));
  }

  async delLiveMatch(id) {}

  get overlays_config() {
    let overlays_config = localStorage.getItem('overlays_config');
    let config = {};
    if (overlays_config) {
      config = JSON.parse(overlays_config);
    }
    return config as any;
  }

  set overlays_config(value) {
    // merge value with current config
    let current_config = this.overlays_config;
    current_config = { ...current_config, ...value };
    // save to localstorage
    localStorage.setItem('overlays_config', JSON.stringify(current_config));
  }

  get score_board_config() {
    let score_board_config = this.overlays_config.score_board;
    return score_board_config;
  }

  set score_board_config(value) {
    let current_config = this.overlays_config;
    let current_score_board_config = current_config.score_board;
    current_score_board_config = { ...current_score_board_config, ...value };
    current_config.score_board = current_score_board_config;
    this.overlays_config = current_config;
  }

  /* media_config = {
    camera_id: string,
    mic_id: string,
  }
  */
  get media_config() {
    let media_config = {};
    if (media_config) {
      media_config = JSON.parse(localStorage.getItem('media_config'));
    }
    return media_config as any;
  }

  set media_config(value) {
    let current_config = this.media_config;
    current_config = { ...current_config, ...value };
    localStorage.setItem('media_config', JSON.stringify(current_config));
  }
}
