import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  constructor(private _http: HttpClient) {
  }

  syncPayments($payment_ids = []) {
    // to string with commas
    let stringIds = $payment_ids.join(',');
    console.log('stringIds', stringIds);

    let formData = new FormData();
    formData.append('payment_ids', stringIds.toString());
    return this._http
      .post<any>(`${environment.apiUrl}/payments/sync-invoices`, formData)
      .pipe(
        map((data) => {
          return data;
        })
      );
  }

  sendReminder($payment_ids = []) {
    // to string with commas
    let stringIds = $payment_ids.join(',');
    console.log('stringIds', stringIds);

    let formData = new FormData();
    formData.append('payment_ids', stringIds.toString());
    return this._http
      .post<any>(`${environment.apiUrl}/payments/send-reminder`, formData)
      .pipe(
        map((data) => {
          return data;
        })
      );
  }

  reGenerateInvoice($payment_ids = [], regeneratorId: string) {

    return this._http
      .post<any>(`${environment.apiUrl}/payments/re-generate-invoices`, {
        payment_ids: $payment_ids.join(','),
        regenerator_id: regeneratorId
      })
      .pipe(
        map((data) => {
          return data;
        })
      );
  }


  //   'payment_ids' => 'required|string',
  // 'refund_payment_method' => 'sometimes|string',
  // 'refund_amount' => 'required|numeric|min:0.01',
  // 'description' => 'sometimes|string',
  refundInvoices(data: {
    payment_ids: string | any[];
    refund_payment_method?: string;
    refund_amount: number;
    description?: string;
  }) {
    let stringIds = data.payment_ids;
    // if payment_ids is string
    if (typeof data.payment_ids != 'string') {
      // to string with commas
      stringIds = data.payment_ids.join(',');
    }

    data.payment_ids = stringIds;
    let formData = new FormData();
    for (let key in data) {
      formData.append(key, data[key]);
    }

    return this._http
      .post<any>(`${environment.apiUrl}/payments/refund-invoices`, formData)
      .pipe(
        map((data) => {
          return data;
        })
      );
  }

  markAsPaid(data: { payment_id: any, method: string, amount?: number, note?: string }) {
    let formData = new FormData();
    for (let key in data) {
      formData.append(key, data[key]);
    }

    return this._http
      .post<any>(
        `${environment.apiUrl}/payments/mark-as-paid-invoices`,
        formData
      )
      .pipe(
        map((data) => {
          return data;
        })
      );
  }

  // get payment details
  getPaymentDetails(payment_id) {
    return this._http
      .get<any>(`${environment.apiUrl}/payments/${payment_id}`)
      .pipe(
        map((data) => {
          return data;
        })
      );
  }

  cancelInvoice(paymentId: string, cancellerId: string) {
    return this._http.post<any>(
      `${environment.apiUrl}/payments/cancel-payment`,
      {
        payment_id: paymentId,
        canceller_id: cancellerId
      }
    ).pipe(
      map((data) => {
        return data;
      })
    );
  }
}
