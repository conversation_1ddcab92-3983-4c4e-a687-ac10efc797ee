import { map } from 'rxjs/operators';
import { HttpHeaders } from '@angular/common/http';
import { environment } from './../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

const optHeaders = new HttpHeaders({
  'Content-Type': 'application/x-www-form-urlencoded',
});

const httpOptions = {
  headers: optHeaders,
};

@Injectable({
  providedIn: 'root',
})
export class ReportService {
  // public variables
  private _currentSeasons: any;
  private _selectedSeason: any;
  private _allClubs: any;

  constructor(private _http: HttpClient) {}

  getReportRegistration($seasonId: number) {
    return this._http
      .get<any>(`${environment.apiUrl}/seasons/${$seasonId}`)
      .pipe(
        map((data) => {
          //Return the data
          return data;
        })
      );
  }
}
