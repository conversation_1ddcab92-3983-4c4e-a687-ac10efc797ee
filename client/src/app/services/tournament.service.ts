import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { query } from '@angular/animations';

@Injectable({
  providedIn: 'root'
})
export class TournamentService {
  constructor(public _http: HttpClient) {
  }

  getAllTournaments() {
    return this._http
      .get(`${environment.apiUrl}/tournaments/getAllTournaments`)
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  getTournament(id) {
    return this._http.get(`${environment.apiUrl}/tournaments/${id}`).pipe(
      map((res: any) => {
        return res;
      }),
      catchError((err) => {
        return throwError(err);
      })
    );
  }

  getTournamentMatches(id) {
    return this._http
      .get(`${environment.apiUrl}/tournaments/${id}/matches`)
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  getMatchesWithQuery(season_id: number, query = '') {
    return this._http
      .get(
        `${environment.apiUrl}/tournaments/season/${season_id}/matches-user${query}`
      )
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(() => err);
        })
      );
  }

  getMatchById(id) {
    return this._http.get(`${environment.apiUrl}/stage-matches/${id}`).pipe(
      map((res: any) => {
        return res;
      }),
      catchError((err) => {
        return throwError(err);
      })
    );
  }

  updateMatch(data, typeAction: 'editMatch' | 'updateScore' | null = null) {
    // console.log(data);
    if (data.id) {
      // new form data
      const formData = new FormData();
      for (const key in data) {
        let value = data[key];
        if (value === null) value = '';
        formData.append(`data[${data.id}][${key}]`, value);
      }
      formData.append('action', 'edit');

      if (typeAction) {
        formData.append('type_action', typeAction);
      }
      return this._http
        .post(`${environment.apiUrl}/stage-matches/editor`, formData)
        .pipe(
          map((res: any) => {
            return res;
          }),
          catchError((err) => {
            return throwError(err);
          })
        );
    }
  }


  getMatchDetails(match_id) {
    return this._http
      .get(`${environment.apiUrl}/stage-matches/${match_id}/details`)
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  updateMatchDetails(data) {
    if (data.match_id) {
      return this._http
        .put(
          `${environment.apiUrl}/stage-matches/${data.match_id}/details`,
          data
        )
        .pipe(
          map((res: any) => {
            return res;
          }),
          catchError((err) => {
            return throwError(err);
          })
        );
    }
  }

  deleteMatchDetails(match_detail_id) {
    return this._http
      .delete(`${environment.apiUrl}/stage-matches/details/${match_detail_id}`)
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  showMatchesFixturesBySeason(season_id, params = {}) {
    // remove null params
    for (const key in params) {
      if (params[key] === null) {
        delete params[key];
      }
    }
    return this._http
      .get(`${environment.apiUrl}/tournaments/season/${season_id}/fixtures`, {
        params
      })
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  checkMatchExist(match_id) {
    return this._http
      .get(`${environment.apiUrl}/stage-matches/${match_id}/exist`)
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  getFixturesResultsByTournament(tournament_id, params = {}) {
    // remove null params
    for (const key in params) {
      if (params[key] === null) {
        delete params[key];
      }
    }
    return this._http
      .get(
        `${environment.apiUrl}/tournaments/${tournament_id}/fixtures-results`,
        { params }
      )
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  fixturesResultsByTeam(team_id, params = {}) {
    // remove null params
    for (const key in params) {
      if (params[key] === null) {
        delete params[key];
      }
    }
    return this._http
      .get(
        `${environment.apiUrl}/tournaments/team/${team_id}/fixtures-results`,
        {
          params
        }
      )
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  // update-score
  updateScore(data: { match_id; home_score; away_score }) {
    if (data.match_id) {
      return this._http
        .post(`${environment.apiUrl}/stage-matches/update-score`, data)
        .pipe(
          map((res: any) => {
            return res;
          }),
          catchError((err) => {
            return throwError(err);
          })
        );
    }
  }

  // update-broadcast-status
  updateBroadcastStatus(data: { match_id; broadcast_status }) {
    if (data.match_id) {
      return this._http
        .post(
          `${environment.apiUrl}/stage-matches/update-broadcast-status`,
          data
        )
        .pipe(
          map((res: any) => {
            return res;
          }),
          catchError((err) => {
            return throwError(err);
          })
        );
    }
  }

  // updateBroadcastData
  updateBroadcastData(data: { match_id; broadcast_data }) {
    if (data.match_id) {
      return this._http
        .post(`${environment.apiUrl}/stage-matches/update-broadcast-data`, data)
        .pipe(
          map((res: any) => {
            return res;
          }),
          catchError((err) => {
            return throwError(err);
          })
        );
    }
  }

  // get user to assign to match
  getUsersToAssign() {
    return this._http.get(`${environment.apiUrl}/match-streaming/users`).pipe(
      map((res: any) => {
        return res;
      }),
      catchError((err) => {
        return throwError(err);
      })
    );
  }

  // assign user to match
  assignUserToMatch(data) {
    return this._http
      .post(`${environment.apiUrl}/match-streaming/assign`, data)
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  // get assigned users
  getAssignedUsers(match_id) {
    return this._http
      .get(`${environment.apiUrl}/match-streaming/${match_id}`)
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  // show user assigned to match
  showUserAssignedToMatch(match_id) {
    return this._http
      .get(`${environment.apiUrl}/match-streaming/${match_id}/show`)
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  getTournamentToRelease(season_id) {
    return this._http
      .post(
        `${environment.apiUrl}/tournaments/all-in-group-sort`, { season_id: season_id }
      )
      .pipe(
        map((res: any) => {
          // const allOrdersZero = res.data.every(
          //   (tournament) => tournament.order === 0
          // );
          // console.log(allOrdersZero, 'allOrdersZero');
          // if (allOrdersZero) {
          //   res.data.sort((a, b) => {
          //     return a.name.localeCompare(b.name, undefined, {
          //       numeric: true,
          //       sensitivity: 'base',
          //     });
          //   });
          // } else {
          //   res.data.sort((a, b) => {
          //     return a.order - b.order;
          //   });
          // }
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  updateTournamentOder(tournaments) {
    return this._http
      .post(`${environment.apiUrl}/settings/update-tournaments-order`, {
        tournaments
      })
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  generateIcs(data): Observable<Blob> {
    return this._http
      .post(`${environment.apiUrl}/locations/generateIcs`, data, { responseType: 'blob' })
      .pipe(
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  assignRoleToUser(data) {
    return this._http
      .post(`${environment.apiUrl}/match-streaming/assign-role`, data)
      .pipe(
        map((res: any) => {
          return res;
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
  }

  getGroupInTournament(tournamentId: string | number) {
    return this._http
      .get(`${environment.apiUrl}/tournaments/${tournamentId}/groups`)
      .pipe(
        map((res: any) => {
          return res;
        })
      );
  }

  getStagesInTournament(tournamentId: string | number) {
    return this._http
      .get(`${environment.apiUrl}/tournaments/${tournamentId}/stages`)
      .pipe(
        map((res: any) => {
          return res;
        })
      );
  }
}
