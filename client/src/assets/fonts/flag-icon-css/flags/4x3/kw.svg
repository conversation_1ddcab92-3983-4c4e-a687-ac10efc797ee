<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg id="svg556" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3023">
  <rdf:RDF>
   <cc:Work rdf:about="">
  <dc:format>image/svg+xml</dc:format>
  <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs558">
  <clipPath id="clipPath4229" clipPathUnits="userSpaceOnUse">
   <rect id="rect4231" fill-opacity="0.67" height="512" width="682.67" y=".000021845" x="0"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath4229)" transform="matrix(.93750 0 0 .93750 0 -.00002048)" stroke-width="1pt">
  <rect id="rect563" height="170.68" width="1024" y="170.64" x="0" fill="#fff"/>
  <rect id="rect559" height="170.68" width="1024" y="341.32" x="0" fill="#f31830"/>
  <rect id="rect564" height="170.68" width="1024" y=".00011734" x="0" fill="#00d941"/>
  <path id="path565" d="m0 0.000022575v512l255.45-170.7 0.55-170.77-256-170.53z"/>
 </g>
</svg>
