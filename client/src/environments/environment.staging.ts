export const environment = {
    production: true,
    hmr: false,
    app_name: '<PERSON><PERSON><PERSON>',
    apiUrl: 'https://staging.ezleague.app/api',
    ezstreamUrl: 'https://livestreameza.web.app',
    proxyUrl: 'https://livekit.longlwu2000.live:8081',
  
    livekit: {
      host: 'ez-league-h9lgqq6l.livekit.cloud',
      wsURL: 'wss://ez-league-h9lgqq6l.livekit.cloud',
      azureStorage: {
        containerName: 'video',
        accountName: 'ezleague',
      },
    },
    red5: {
      protocol: 'wss',
      port: null,
      host: 'red5pro.longlwu2000.live',
      app: 'live',
    },
    storageUrl: 'https://staging.ezleague.app/storage/',
    firebase: {
      apiKey: "AIzaSyAnrBN-bHvUIMW7kfkJOz4Z5S9h8ZSDrEo",
      authDomain: "ezl-staging.firebaseapp.com",
      projectId: "ezl-staging",
      storageBucket: "ezl-staging.firebasestorage.app",
      messagingSenderId: "************",
      appId: "1:************:web:9ec9bfb31389c71e5b4dfa",
      vapidKey: "BEq22TcF44yLnKMSYprmPGivxi8Dx3deYWw9ytXQ82lDUZDikkFrB9Lr0Fu2nkuHOzXMGu9AJM5H12AbRbn4mPA",
      measurementId: "G-3SXVJLTRQK"
    },
    stripe: {
      publishableKey:
        'pk_test_51NQ3UmDXDLBGPpQrT99fYhZ3lmv2GrN667trK2Kuyp8Of9z4wqr7n73UWqmIAwlp3pvV0B6wg6Clv3JYmle4bsqV00a7Nb4Lba',
    },
  };
  