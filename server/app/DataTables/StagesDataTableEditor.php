<?php

namespace App\DataTables;

use App\Models\Stage;
use App\Models\StageMatch;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Yajra\DataTables\DataTablesEditor;

class StagesDataTableEditor extends DataTablesEditor
{
    protected $model = Stage::class;

    /**
     * Get create action validation rules.
     */
    public function createRules(): array
    {
        return [
            'type' => 'sometimes|max:50',
            'name' => 'required|max:191',
            'tournament_id' => 'required|exists:tournaments,id',
            'sport_type' => 'required|in:football,basketball,rugby,baseball,american_football',
            'no_encounters' => 'sometimes|numeric',
            'ranking_criteria' => 'sometimes|max:191',
        ];
    }

    /**
     * Get edit action validation rules.
     */
    public function editRules(Model $model): array
    {
        return [
            'type' => 'required|max:50',
            'name' => 'required|max:191',
            'tournament_id' => 'required|exists:tournaments,id',
            'sport_type' => 'sometimes|in:football,basketball,rugby,baseball,american_football',
            'no_encounters' => ['required','numeric',
                function ($attribute, $value, $fail) use ($model) {
                    if ($model->no_encounters != $value) {
                        $hasMatches = StageMatch::where('stage_id', $model->id)->exists();
                        if ($hasMatches) {
                            return $fail(__('Cannot update because the match has been generated.'));
                        }
                    }
                }
            ],
            'ranking_criteria' => 'required|max:191',
        ];
    }

    /**
     * Get remove action validation rules.
     */
    public function removeRules(Model $model): array
    {
        return [];
    }

    public function creating(Model $model, array $data): array
    {
        // do something before creating the model
        Log::info($data);
        return $data;
    }

    public function updating(Model $model, array $data)
    {
        // do something before updating the model
        return $data;
    }

    /**
     * Event hook that is fired after `creating` and `updating` hooks, but before
     * the model is saved to the database.
     */
    public function saving(Model $model, array $data): array
    {

        if (isset($data['sport_type'])) {
            $sport_type = $data['sport_type'];
            $sport_config = config('constants.sport_types.' . $sport_type);
            $data['points_win'] = $sport_config['points_win'];
            $data['points_draw'] = $sport_config['points_draw'] ?? 0;
            $data['points_loss'] = $sport_config['points_loss'];
        }

        if (isset($data['is_released'])) {
            if ($data['is_released'] == 'true' || $data['is_released'] == '1' || $data['is_released'] == 1) {
                $data['is_released'] = true;
            } else {
                $data['is_released'] = false;
            }
        }

        if (isset($data['is_display_tbd'])) {
            if ($data['is_display_tbd'] == 'true' || $data['is_display_tbd'] == '1' || $data['is_display_tbd'] == 1) {
                $data['is_display_tbd'] = true;
            } else {
                $data['is_display_tbd'] = false;
            }
        }

        if (isset($data['is_display_results'])) {
            if ($data['is_display_results'] == 'true' || $data['is_display_results'] == '1' || $data['is_display_results'] == 1) {
                $data['is_display_results'] = true;
            } else {
                $data['is_display_results'] = false;
            }
        }

        if (isset($data['is_display_table'])) {
            if ($data['is_display_table'] == 'true' || $data['is_display_table'] == '1' || $data['is_display_table'] == 1) {
                $data['is_display_table'] = true;
            } else {
                $data['is_display_table'] = false;
            }
        }

        if (isset($data['third_place'])) {
            if ($data['third_place'] == 'true' || $data['third_place'] == '1' || $data['third_place'] == 1) {
                $data['third_place'] = true;
            } else {
                $data['third_place'] = false;
            }
        }

        return $data;
    }

    /**
     * Event hook that is fired after `created` and `updated` events.
     */
    public function saved(Model $model, array $data): Model
    {
        // do something after saving the model
        return $model;
    }
}
