<?php

namespace App\Http\Controllers;

use App\DataTables\ClubsDataTableEditor;
use App\Models\Club;
use App\Http\Controllers\Controller;
use App\Models\TeamCoach;
use Http\Discovery\Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class ClubController extends Controller
{

    private $urlRegex;

    private $s3Instance;

    public function __construct()
    {
        $this->urlRegex = config("constants.regex.url", null);
        $this->s3Instance = new S3Controller();
    }

    public function all(Request $request)
    {
        $clubs = Club::orderBy('name', 'asc')->get();
        foreach ($clubs as $club) {
            if (!preg_match($this->urlRegex, $club->logo) && !str_contains($club->logo, 'assets/')) {
                $club->filePath = $club->logo;
                $club->logo = $this->s3Instance->getObjectUrl($club->logo);
            }
        }
        return DataTables::of($clubs)->make(true);
    }

    public function getCurrentLogoPath($clubId)
    {
        $club = Club::find($clubId);
        if (!$club) {
            return false;
        }

        return $club->logo;

    }

    public function editor(ClubsDataTableEditor $editor)
    {

        try {
            $action = request()->get('action');
            $requestData = request()->get("data");

            foreach ($requestData as $dataId => $rqValue) {

                $actualPath = str_contains($rqValue['logo'], 'temp/') ? explode("temp/", $rqValue['logo'])[1] : $rqValue['logo'];
                if ($action == "create") {
                    if (empty($rqValue['logo'])) {
                        return response()->json([
                            'fieldErrors' => [
                                [
                                    'name' => 'logo',
                                    'status' => "Club logo is required",
                                ],
                            ],
                        ], 422);
                    }
                    $requestData[$dataId]['logo'] = $actualPath;
                    $this->s3Instance->moveObject($rqValue['logo'], $actualPath);
                    request()->merge([
                        "action" => $action,
                        'data' => $requestData
                    ]);
                } else if ($action == "edit") {
                    $currentLogoPath = $this->getCurrentLogoPath($dataId);

                    // Check logo is not empty and not a link
                    if ($rqValue['logo'] && !preg_match($this->urlRegex, $rqValue['logo'])) {

                        //Check if request logo value is not the same as the current logo value
                        if ($rqValue['logo'] != $currentLogoPath) {
                            // Check if current logo path is not a link
                            if (!preg_match($this->urlRegex, $currentLogoPath)) {
                                // Delete current logo on S3
                                $this->s3Instance->deleteObject($currentLogoPath);
                            }
                            $requestData[$dataId]['logo'] = $actualPath;
                            $this->s3Instance->moveObject($rqValue['logo'], $actualPath);
                        }
                    } else {
                        $requestData[$dataId]['logo'] = $currentLogoPath;
                    }

                    Log::info("requestData", [$requestData]);

                    request()->merge([
                        "action" => $action,
                        'data' => $requestData
                    ]);
                } else if ($action == "remove") {
                    // Get current logo path in DB
                    $currentLogoPath = $this->getCurrentLogoPath($dataId);

                    if (!preg_match($this->urlRegex, $currentLogoPath)) {
                        $this->s3Instance->deleteObject($currentLogoPath);
                    }
                }
            }

            $results = $editor->process(request())->getContent();

            $responseData = json_decode($results, true);

            if (isset($responseData['fieldErrors']) && !empty($responseData['fieldErrors'])) {
                return response()->json($responseData, 400);
            }

            if ($action != "remove") {
                foreach ($responseData['data'] as $key => $value) {
                    if (!preg_match($this->urlRegex, $responseData['data'][$key]['logo']) && !str_contains($responseData['data'][$key]['logo'], 'assets/')) {
                        $responseData['data'][$key]['logo'] = $this->s3Instance->getObjectUrl($responseData['data'][$key]['logo']);
                    }
                }
            }

            return response()->json($responseData);
        } catch (Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }

    // Get all clubs is_active = 1
    public function getClubsActive(Request $request)
    {
        $clubs = Club::where('is_active', 1)
            ->orderBy('name', 'asc')
            ->get();
        return response()->json($clubs, 200);
    }

    public function toggleActive(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:clubs,id',
        ]);

        $club = Club::find($request->id);

        $club->is_active = !$club->is_active;
        $club->save();
        return response()->json($club, 200);
    }

    public function getClubByUser()
    {
        $data = Club::whereHas('userClubs', function ($query) {
            $query->where('user_id', Auth::user()->id);
        })
            ->where('is_active', 1)
            ->get();
        return DataTables::of($data)->make(true);
    }

    // get user in club
    public function getUsersByClub($club_id)
    {

        $club = Club::find($club_id);
        if (!$club) {
            return response()->json(['message' => __('validation.exists', ['attribute' => __('validation.attributes.club_id')])], 400);
        }
        $data = $club->users()->get();
        return DataTables::of($data)->make(true);
    }

    /**
     * Get all coaches by club id
     * @param $club_id
     */
    public function getCoachesByClub($club_id, Request $request)
    {
        $team_id = $request->team_id;
        $coaches = TeamCoach::select('users.*')
            ->join('teams', 'team_coaches.team_id', '=', 'teams.id')
            ->join('users', 'team_coaches.user_id', '=', 'users.id')
            ->where('teams.club_id', $club_id)
            ->distinct();

        // remove coach in team
        if ($team_id) {
            $coaches->whereNotIn('users.id', function ($query) use ($team_id) {
                $query->select('user_id')
                    ->from('team_coaches')
                    ->where('team_id', $team_id);
            });
        }

        $coaches = $coaches->get();

        return DataTables::of($coaches)->make(true);
    }
}
