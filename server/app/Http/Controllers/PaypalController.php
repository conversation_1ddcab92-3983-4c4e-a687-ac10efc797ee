<?php

namespace App\Http\Controllers;

use App\Http\Requests\Auth\LoginRequest;
use App\Models\Payment;
use App\Models\PaymentDetail;
use App\Models\Registration;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Srmklive\PayPal\Services\PayPal;
use function Laravel\Prompts\note;

class PaypalController extends Controller
{

    public function handleWebhook(Request $request)
    {
        // Log::info('Paypal webhook', $request->all());
        $request->validate([
            'event_type' => 'required'
        ]);
        // Log::info('Paypal webhook', ['event_type' => $request->event_type, 'invoice_id' => $request->resource['id'], 'status' => $request->resource['status']]);
        $payload = $request->all();
        $name_event = str_replace(' ', '', ucwords(strtolower(str_replace('.', ' ', $payload['event_type']))));
        $method = "handle$name_event";
        // emit event
        event(new \App\Events\PaypalWebhookEvents($payload));
        // Log::info('Paypal webhook method', ['method' => $method]);
        if (method_exists($this, $method)) {
            $response = $this->{$method}($payload);
            return $response;
        }

        return $request;
    }

    function handleInvoicingInvoiceCreated($payload)
    {
        Log::info('Paypal webhook handleInvoicingInvoiceCreated', $payload);
        return $payload;
    }

    function handleInvoicingInvoiceRefunded($payload)
    {
        Log::info('Paypal webhook handleInvoicingInvoiceRefunded', $payload);
        $invoice = $this->getInvoiceFromPayload($payload);
//        $this->updateStatusInvoice($invoice['id'], $invoice['status']); // update later
        return $payload;
    }

    function handleInvoicingInvoiceCancelled($payload)
    {
        Log::info('Paypal webhook handleInvoicingInvoiceCancelled', $payload);
        $invoice = $this->getInvoiceFromPayload($payload);
//        $this->updateStatusInvoice($invoice['id'], $invoice['status']); // update later
        return $payload;
    }

    function handleInvoicingInvoicePaid($payload)
    {
        Log::info('Paypal webhook handleInvoicingInvoicePaid', $payload);
        $invoice = $this->getInvoiceFromPayload($payload);
//        $this->updateStatusInvoice($invoice['id'], $invoice['status']); // update later
        return $payload;
    }

    function handleInvoicingInvoiceScheduled($payload)
    {
        Log::info('Paypal webhook handleInvoicingInvoiceScheduled', $payload);
        return $payload;
    }

    function handleInvoicingInvoiceUpdated($payload)
    {
        Log::info('Paypal webhook handleInvoicingInvoiceUpdated', $payload);
        return $payload;
    }

    function getInvoiceFromPayload($payload)
    {
        $invoice = null;
        if ($payload['resource_type'] == 'invoices') {
            if (isset($payload['resource']['invoice'])) {
                $invoice = $payload['resource']['invoice'];
                $invoice['status'] = strtolower($invoice['status']);
                $invoice['number'] = $invoice['detail']['invoice_number'];
            } else {
                $invoice = $payload['resource'];
            }
        }
        return $invoice;
    }

    private function createPaypalInstance()
    {
        $provider = new PayPal();
        $provider->setApiCredentials(config('paypal'));
        $provider->getAccessToken();

        return $provider;
    }

    private function createInvoicePayload($invoiceNo, $invoiceItems, $invoiceRecipient, $invoiceCurrency)
    {
        return [
            'detail' => [  // Required in v2 API
                'invoice_number' => $invoiceNo,  // Your custom invoice number
//                'invoice_date' => Carbon::now('UTC')->subDay()->toDateString(),
                'currency_code' => $invoiceCurrency,
                "payment_term" => [
                    "term_type" => "NET_10",
                ]
            ],
            'primary_recipients' => [  // Changed from billing_info in v2
                [  // Each recipient is wrapped in an array
                    'billing_info' => [
                        'email_address' => $invoiceRecipient['email'],
                        'name' => [
                            'given_name' => $invoiceRecipient['first_name'],
                            'surname' => $invoiceRecipient['last_name'],
                        ]
                    ]
                ]

            ],
            "invoicer" => [
                "business_name" => config("app.name"),
            ],
            'items' => $invoiceItems,
        ];
    }


    public function createInvoiceBase($invoiceNo, $invoiceItems, $invoiceRecipient, $invoiceCurrency)
    {
        $paypalInstance = $this->createPaypalInstance();

        $invoicePayload = $this->createInvoicePayload(
            $invoiceNo,
            $invoiceItems,
            $invoiceRecipient,
            $invoiceCurrency
        );

        try {
            $draftInvoice = $paypalInstance->createInvoice($invoicePayload);

            if (isset($draftInvoice['error'])) {
                Log::error("Error while creating draft invoice", [$draftInvoice['error']]);

                if ($draftInvoice['error']['details'][0]['issue'] == 'DUPLICATE_INVOICE_NUMBER') {
                    $invoicePayload = $this->createInvoicePayload(
                        $invoiceNo . "-" . time(),
                        $invoiceItems,
                        $invoiceRecipient,
                        $invoiceCurrency
                    );
                    $draftInvoice = $paypalInstance->createInvoice($invoicePayload);
                } else {
                    return [
                        "success" => false,
                        "error" => $draftInvoice['error']
                    ];
                }
            }
            $invoiceId = null;

            if (isset($draftInvoice['href'])) {
                $invoiceId = explode('/', $draftInvoice['href']);
                $invoiceId = end($invoiceId);
            }

            $draftInvoice['invoice_no'] = $invoiceNo;
            $draftInvoice['invoice_id'] = $invoiceId;

            return [
                "success" => true,
                "message" => "Invoice created successfully",
                "data" => $draftInvoice
            ];
        } catch (Exception $error) {
            throw new Exception('createInvoiceBase - Paypal Error: ', $error->getMessage());
        }
    }

    public function sendInvoiceBase($invoiceId)
    {
        try {
            $paypalInstance = $this->createPaypalInstance();

            $subject = "Invoice #" . $invoiceId . " from EZ Active";

            $note = "Thank you for your business. Test invoice";

            $sendStatus = json_decode($paypalInstance->sendInvoice($invoiceId, $subject, $note), true);

            if (!isset($sendStatus['href'])) {
                Log::error("Error while sending invoice", [$sendStatus]);
                return [
                    "success" => false,
                    "error" => $sendStatus
                ];
            } else {
                return [
                    "success" => true,
                    "message" => "Send invoice successfully",
                    "data" => $sendStatus
                ];
            }
        } catch (Exception $error) {
            throw new Exception('sendInvoiceBase - Paypal Error: ', $error->getMessage());
        }
    }

    public function getInvoiceBase($invoiceId)
    {
        try {
            $paypalInstance = $this->createPaypalInstance();

            $invoice = $paypalInstance->showInvoiceDetails($invoiceId);


            // Handle Paypal identity errors
            if (isset($invoice['error'])) {
                Log::error("Paypal Identity - Error while getting invoice", [$invoice['error']]);
                return [
                    "success" => false,
                    "error" => $invoice['error']
                ];
            }

            // Handle Paypal data errors

            if (isset($invoice['details'])) {
                Log::error("Paypal Data - Error while getting invoice", [$invoice]);
                return [
                    "success" => false,
                    "error" => $invoice,
                ];
            }

            return [
                "success" => true,
                "message" => "Get invoice successfully",
                "data" => $invoice
            ];
        } catch (Exception $error) {
            throw new Exception('getInvoiceBase - Paypal Error: ', $error->getMessage());
        }
    }

    public function markAsPaidInvoice($invoiceId, $invoiceCurrency, $paymentMethod, $paymentDate, $amount, $note = '')
    {
        $paypalInstance = $this->createPaypalInstance();

        $paypalInstance->setCurrency($invoiceCurrency);

        $status = $paypalInstance->registerPaymentInvoice($invoiceId, $paymentDate, $paymentMethod, $amount, $note ?? '');

        // Handle Paypal identity errors
        if (isset($invoice['error'])) {
            Log::error("Paypal Identity - Error while getting invoice", [$invoice['error']]);
            return [
                "success" => false,
                "error" => $invoice['error']
            ];
        }

        // Handle Paypal data errors

        if (isset($invoice['details'])) {
            Log::error("Paypal Data - Error while getting invoice", [$invoice]);
            return [
                "success" => false,
                "error" => $invoice,
            ];
        }

        return [
            "success" => true,
            "message" => "Invoice marked as paid successfully",
            "data" => $status
        ];
    }

    public function refundInvoiceBase($invoiceId, $refundDate, $refundMethod, $amount)
    {
        $paypalInstance = $this->createPaypalInstance();

        try {
            $refundStatus = $paypalInstance->refundInvoice($invoiceId, $refundDate, $refundMethod, $amount);

            // Handle Paypal identity errors
            if (isset($refundStatus['error'])) {
                Log::error("Paypal Identity - Error while getting invoice", [$refundStatus['error']]);
                return [
                    "success" => false,
                    "error" => $refundStatus['error']
                ];
            }

            // Handle Paypal data errors
            if (isset($refundStatus['details'])) {
                Log::error("Paypal Data - Error while getting invoice", [$refundStatus]);
                return [
                    "success" => false,
                    "error" => $refundStatus,
                ];
            }

            return [
                "success" => true,
                "message" => "Invoice refunded successfully",
                "data" => $refundStatus
            ];


        } catch (Exception $error) {
            throw new Exception('refundInvoiceBase - Paypal Error: ', $error->getMessage());
        }
    }

    public function sendReminderBase($invoiceId, $subject, $note)
    {
        $paypalInstance = $this->createPaypalInstance();

        $reminderStatus = $paypalInstance->sendInvoiceReminder($invoiceId, $subject, $note);

        // Handle Paypal identity errors
        if (isset($reminderStatus['error'])) {
            Log::error("Paypal Identity - Error while getting invoice", [$reminderStatus['error']]);
            return [
                "success" => false,
                "error" => $reminderStatus['error']
            ];
        }

        // Handle Paypal data errors
        if (isset($reminderStatus['details'])) {
            Log::error("Paypal Data - Error while getting invoice", [$reminderStatus]);
            return [
                "success" => false,
                "error" => $reminderStatus,
            ];
        }

        return [
            "success" => true,
            "data" => $reminderStatus,
        ];

    }

    public function cancelInvoiceBase($invoiceId)
    {
        $paypalInstance = $this->createPaypalInstance();

        $cancelStatus = $paypalInstance->cancelInvoice($invoiceId);

        Log::info("cancelStatus", [$cancelStatus]);

        // Handle Paypal identity errors
        if (isset($cancelStatus['error'])) {
            Log::error("Paypal Identity - Error while getting invoice", [$cancelStatus['error']]);
            return [
                "success" => false,
                "error" => $cancelStatus['error']
            ];
        }

        // Handle Paypal data errors
        if (isset($cancelStatus['details'])) {
            Log::error("Paypal Data - Error while getting invoice", [$cancelStatus]);
            return [
                "success" => false,
                "error" => $cancelStatus,
            ];
        }

        return [
            "success" => true,
            "data" => $cancelStatus,
        ];
    }


    public function handleCreateAndSendInvoice($requestItems, $invoiceRecipient, ?Registration $registration = null, ?string $customInvoiceNo = '')
    {

        try {
            $invoiceCurrency = $request->currency ?? config('constants.payment_currency');
            $invoiceItems = [];

            foreach ($requestItems as $item) {
                $newItem = [
                    'name' => $item['name'], // Required
                    'quantity' => intval($item['quantity']), // Required
//                    'unit_amount' => $item['unit_amount'],
                    'unit_amount' => $item['unit_amount'] ?? [
                            'currency_code' => $invoiceCurrency,
                            'value' => $item['amount'],
                        ],
                ];
                $invoiceItems[] = $newItem;
            }


            if ($customInvoiceNo) {
                $invoiceNo = $customInvoiceNo;
            } else if ($registration) {
                $invoiceNo = config('app.short_name') . "-" . $registration->id;
            } else {
                $invoiceNo = "INV-" . time();
            }


            // Create invoice
            $draftInvoice = $this->createInvoiceBase($invoiceNo, $invoiceItems, $invoiceRecipient, $invoiceCurrency);


            if (!$draftInvoice['success']) {
                return ["success" => false,
                    "error" => $draftInvoice['error']];
            }

            // Send invoice
            $sendStatus = $this->sendInvoiceBase($draftInvoice['data']['invoice_id']);

            if (!$sendStatus['success']) {
                return [
                    "success" => false,
                    "error" => $sendStatus['error']
                ];
            }

            $invoiceDetails = $this->getInvoiceBase($draftInvoice['data']['invoice_id']);

            $invoiceDetails['data']['payment_url'] = $sendStatus['data']['href'];

            if (!$invoiceDetails['success']) {
                return [
                    "success" => false,
                    "error" => $invoiceDetails['error']
                ];
            }

            Log::info("invoiceDetails", [$invoiceDetails]);

            return [
                "success" => true,
                "message" => "Invoice created and sent successfully",
                "data" => $invoiceDetails['data'],
            ];
        } catch (Exception $error) {
            Log::error('handleCreateAndSendInvoice - Paypal Error: ', [$error->getMessage()]);
            return [
                "success" => false,
                "error" => $error->getMessage()
            ];
        }
    }

    public function handleCreateAndSendInvoiceAPI(Request $request)
    {
        $request->validate([
            'items' => 'required|array',
            'recipient' => 'sometimes|array',
        ]);

        $requestItems = $request->get('items');
        $invoiceRecipient = $request->get('recipient');

        return response()->json($this->handleCreateAndSendInvoice($requestItems, $invoiceRecipient));
    }

    public function handleMarkAsPaidAPI(Request $request)
    {
        $request->validate([
            'payment_id' => 'required',
            'method' => 'required|string',
            'amount' => 'sometimes|numeric|min:0.01',
            'note' => 'sometimes|nullable|string',
        ]);

        Log::info('Paypal record payment', $request->all());

        $paymentId = $request->get('payment_id');
        $paymentInfo = Payment::find($paymentId);
        $invoiceId = $paymentInfo->invoice_id;
        $paymentDate = date('Y-m-d');
        $paymentMethod = $request->get('method');
        $invoiceCurrency = strtoupper($request->currency ?? config('constants.payment_currency'));

        Log::info('Paypal record payment', [$invoiceId, $paymentDate, $paymentMethod, $invoiceCurrency]);

        $amount = isset($request->amount) ? $request->amount : $paymentInfo->amount;
        $note = $request->note ?? '';

        return response()->json($this->markAsPaidInvoice($invoiceId, $invoiceCurrency, $paymentMethod, $paymentDate, $amount, $note), 200);
    }

    public function handleGetInvoiceDetailAPI(Request $request)
    {
        $request->validate([
            'invoice_id' => 'required|string',
        ]);

        $invoiceId = $request->get('invoice_id');

        try {

            return response()->json($this->getInvoiceBase($invoiceId));

        } catch (Exception $error) {
            Log::error('handleGetInvoiceDetailAPI - Paypal Error: ', [$error->getMessage()]);
            return response()->json(['message' => $error->getMessage()], 400);
        }
    }

}
