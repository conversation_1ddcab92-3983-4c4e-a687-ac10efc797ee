<?php

namespace App\Http\Controllers;

use App\DataTables\StageMatchesDataTableEditor;
use App\Models\Group;
use App\Models\Location;
use App\Models\Payment;
use App\Models\Player;
use App\Models\Rankings;
use App\Models\Registration;
use App\Models\ScheduleMatch;
use App\Models\Settings;
use App\Models\Stage;
use App\Models\StageMatch;
use App\Models\User;
use Carbon\Carbon;
use DateInterval;
use DateTime;
use DateTimeZone;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class StageMatchController extends Controller
{

    private $stageMatchRefereeController;

    private $urlRegex;
    private $s3Instance;

    public function __construct()
    {
        $this->stageMatchRefereeController = new StageMatchRefereeController();
        $this->urlRegex = config("constants.regex.url", null);
        $this->s3Instance = new S3Controller();
    }

    public function checkAllMatches()
    {
        $matches = StageMatch::all();
        if ($matches->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No matches found for the given stage.',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'matches' => 'There are matches in the system.',
        ], 200);
    }


    //get all matches in a stage by stage id
    public function allInStage($stage_id)
    {
        $timezone = request()->header('X-Time-Zone') ?? '+00:00';
        $newtimeZone = new DateTimeZone($timezone);

        $teams = Stage::where('id', $stage_id)
            ->get()[0]->teams;
        $teams = $teams->map(function ($team) {
            return [
                'value' => $team->id,
                'label' => $team->name,
            ];
        });

        $matches = StageMatch::with(['homeTeam.club', 'awayTeam.club', 'location', 'homeLabel', 'awayLabel', 'referees'])
            ->orderBy('start_time', 'asc')
            ->orderBy('match_number', 'asc')
            ->where('stage_id', $stage_id)
            ->get();

        $rankings = Rankings::where('stage_id', $stage_id)
            ->get()
            ->map(function ($ranking) {
                return [
                    'value' => $ranking->id,
                    'label' => $ranking->label,
                ];
            })
            ->sortBy('label')
            ->values();

        return DataTables::of($matches)
            ->with(
                'options',
                [
                    'teams' => $teams,
                    'location' => Location::select('id as value', 'name as label')
                        ->orderBy('name', 'asc')
                        ->get(),
                    'rank_label' => $rankings,
                ]
            )
            ->addColumn('start_time_short', function ($match) use ($newtimeZone) {
                if (is_null($match->start_time) || ($match->start_time == $match->end_time)) {
                    return 'TBD';
                }
                // Use consistent timezone conversion approach
                $time_short = Carbon::parse($match->start_time, config('app.timezone'))->setTimezone($newtimeZone);
                return $time_short->format('H:i');
            })
            ->addColumn('end_time_short', function ($match) use ($newtimeZone) {
                if (is_null($match->end_time) || ($match->start_time == $match->end_time)) {
                    return 'TBD';
                }
                $time_short = Carbon::parse($match->end_time, config('app.timezone'))->setTimezone($newtimeZone);
                return $time_short->format('H:i');
            })
            ->addColumn('location_name', function ($match) {
                return is_null($match->location) ? 'TBD' : $match->location->name;
            })
            ->addColumn('home_team_name', function ($match) {
                return is_null($match->homeTeam) ? 'TBD' : $match->homeTeam->name;
            })
            ->addColumn('away_team_name', function ($match) {
                return is_null($match->awayTeam) ? 'TBD' : $match->awayTeam->name;
            })
            ->editColumn('home_score', function ($match) {
                return is_null($match->home_score) ? '' : $match->home_score;
            })
            ->editColumn('away_score', function ($match) {
                return is_null($match->away_score) ? '' : $match->away_score;
            })
            ->addColumn('date', function ($match) use ($newtimeZone) {
                if (is_null($match->start_time)) {
                    if (is_null($match->end_time)) {
                        return 'TBD';
                    } else {
                        $date = Carbon::parse($match->end_time, config('app.timezone'))->setTimezone($newtimeZone);
                        return date('Y-m-d', strtotime($date));
                    }
                } else {
                    $date = Carbon::parse($match->start_time, config('app.timezone'))->setTimezone($newtimeZone);
                    return date('Y-m-d', strtotime($date));
                }
            })
            ->addColumn('group_round', function ($match) {
                //split round name by - and get the first part
                $round_name = trim(explode('-', $match->round_name)[0]);
                return $round_name;
            })
            ->addColumn('referees', function ($match) {
                foreach ($match->referees as $referee) {
                    if (!is_null($referee->user_id)) {
                        $referee->user = User::find($referee->user_id);
                    }
                }
                return $match->referees->toArray();
            })
            ->make(true);
    }

    public function getDashboardTable($seasonId)
    {
        $groups = Group::where('season_id', $seasonId)->get();
        $result = [];
        $now = Carbon::now();
        // $now = Carbon::create(2023, 10, 1, 0, 0, 0);

        foreach ($groups as $group) {
            // Get a list of all teams that belong to this group
            $teamIds = $group->teams->pluck('id')->toArray();

            // Count the number of matches played
            $playedMatchesCount = StageMatch::where(function ($query) use ($teamIds) {
                $query->whereIn('home_team_id', $teamIds)
                    ->orWhereIn('away_team_id', $teamIds);
            })
                ->whereNotNull('start_time')
                ->whereNotNull('end_time')
                ->where('end_time', '<', $now)
                ->count();

            // Count the number of games scheduled but not yet played
            $scheduledMatchesCount = StageMatch::where(function ($query) use ($teamIds) {
                $query->whereIn('home_team_id', $teamIds)
                    ->orWhereIn('away_team_id', $teamIds);
            })
                ->whereNotNull('start_time')
                ->whereNotNull('end_time')
                ->where('end_time', '>=', $now)
                ->count();

            // Count the number of unscheduled rounds (no start and end time yet)
            $unscheduledMatchesCount = StageMatch::where(function ($query) use ($teamIds) {
                $query->whereIn('home_team_id', $teamIds)
                    ->orWhereIn('away_team_id', $teamIds);
            })
                ->whereNull('start_time')
                ->whereNull('end_time')
                ->count();

            $totalMatchesCount = StageMatch::where(function ($query) use ($teamIds) {
                $query->whereIn('home_team_id', $teamIds)
                    ->orWhereIn('away_team_id', $teamIds);
            })
                ->count();

            $result[] = [
                'group_id' => $group->id,
                'group_name' => $group->name,
                'count_played_match' => $playedMatchesCount,
                'count_scheduled_match' => $scheduledMatchesCount,
                'count_unscheduled_match' => $unscheduledMatchesCount,
                'count_match' => $totalMatchesCount,
            ];
        }

        // Sort the result based on group_name
        $result = collect($result)->sortBy(function ($item) {
            $name = $item['group_name'];
            $matches = [];

            // Separate numbers and letters in the group name
            preg_match('/(\d+)/', $name, $matches);
            $number = count($matches) > 0 ? intval($matches[0]) : 0;
            $stringPart = preg_replace('/\d+/', '', $name);
            return [$number, $stringPart];
        })->values();

        return DataTables::of($result)
            ->with([
                'recordsTotal' => count($result),
                'recordsFiltered' => count($result)
            ])
            ->make(true);
    }

    public function getSeasonDashboardInfo($season_id)
    {
        $groups = Group::where('season_id', $season_id)->get();
        $countMatches = 0;
        foreach ($groups as $group) {
            $teamIds = $group->teams->pluck('id')->toArray();
            $totalMatchesCount = StageMatch::where(function ($query) use ($teamIds) {
                $query->whereIn('home_team_id', $teamIds)
                    ->orWhereIn('away_team_id', $teamIds);
            })
                ->count();

            $countMatches += $totalMatchesCount;
        }
        // Count the number of registered players
        $registeredPlayers = Registration::where('season_id', $season_id)
            ->whereIn('approval_status', [
                config('constants.approve_status.registered'),
                config('constants.approve_status.approved'),
            ])
            ->count();

        // Count the number of players waiting for status update
        $awaitingUpdate = Player::whereHas('registrations', function ($query) use ($season_id) {
            $query->where('season_id', $season_id);
        })->where('validate_status', 'Awaiting Update')->count();

        // Count the number of invoices sent
        $sentPayments = Payment::whereHas('paymentDetails.registration', function ($query) use ($season_id) {
            $query->where('season_id', $season_id);
        })
            ->whereIn('status', ['SENT', 'open'])
            ->count();

        return response()->json([
            'status' => 'OK',
            'info' => [
                'count_matches' => $countMatches,
                'registered_players' => $registeredPlayers,
                'awaiting_update' => $awaitingUpdate,
                'sent_payments' => $sentPayments,
            ]
        ]);
    }

    public function editor(StageMatchesDataTableEditor $editor)
    {
        $currentTimeZone = new DateTimeZone(request()->header('X-Time-Zone') ?? '+00:00');

        if (request()->get('action') == 'edit' && request()->get('type_action') == 'updateScore') {
            foreach (request()->get('data') as $key => $value) {
                $canUpdate = $this->checkValidEditTime($key, $currentTimeZone);
                if (!$canUpdate['success']) {
                    return response()->json([
                        'error' => $canUpdate['message']
                    ], 400);
                }
            }
        }

        return $editor->process(request());
    }


    // swap home and away team in stage_match
    public function swapTeams(Request $request)
    {
        $request->validate([
            'stage_id' => 'required|integer',
            'home_team_id' => 'nullable|integer',
            'away_team_id' => 'nullable|integer',
            'home_score' => 'nullable|integer',
            'away_score' => 'nullable|integer',
            'home_penalty' => 'nullable|integer',
            'away_penalty' => 'nullable|integer',
        ]);

        $match = StageMatch::find($request->stage_id);

        if ($match) {
            // Swap team IDs
            $match->home_team_id = $request->away_team_id;
            $match->away_team_id = $request->home_team_id;

            // Swap scores if provided
            if ($request->has('home_score')) {
                $match->home_score = $request->away_score;
            } else {
                $match->home_score = null;
            }

            if ($request->has('away_score')) {
                $match->away_score = $request->home_score;
            } else {
                $match->away_score = null;
            }

            // Swap penalties if provided
            if ($request->has('home_penalty')) {
                $match->home_penalty = $request->away_penalty;
            } else {
                $match->home_penalty = null;
            }

            if ($request->has('away_penalty')) {
                $match->away_penalty = $request->home_penalty;
            } else {
                $match->away_penalty = null;
            }

            $currentHomeLabelId = $match->home_label_id;
            $currentAwayLabelId = $match->away_label_id;

            $match->home_label_id = $currentAwayLabelId;
            $match->away_label_id = $currentHomeLabelId;

            $match->save();

            return response()->json(true, 200);
        } else {
            // Match not found
            $message = 'Match not found';
            return response()->json($message, 400);
        }
    }

    // reset score by stage_match_id
    public function resetScoreById($stage_match_id)
    {
        $match = StageMatch::find($stage_match_id);

        if ($match) {
            $match->home_score = null;
            $match->away_score = null;
            $match->save();
            return response()->json(true, 200);
        }
        return response()->json(false, 200);
    }

    // reset score multiple by stage_match_ids
    public function resetScoreMultiple(Request $request)
    {
        $request->validate([
            'stage_match_ids' => 'required|string'
        ]);
        $stage_match_ids = $request->stage_match_ids;
        // convert string to array
        $stage_match_ids = explode(',', $stage_match_ids);
        $matches = StageMatch::whereIn('id', $stage_match_ids)->get();
        foreach ($matches as $match) {

            $match->home_score = null;
            $match->away_score = null;
            $match->home_penalty = null;
            $match->away_penalty = null;
            $match->save();

        }
        return response()->json(true, 200);
    }

    // get all matches in tournament by tournament id
    public function matchesInTournament($tournament_id)
    {
        $matches = StageMatch::selectRaw('*,DATE_FORMAT(start_time,"%Y-%m-%d") as date')
            ->with(['homeTeam', 'awayTeam', 'location'])
            ->whereHas('stage', function ($query) use ($tournament_id) {
                $query->where('tournament_id', $tournament_id);
            })
            ->get();
        // group matches by stage
        $matches = $matches->groupBy('date');
        return response()->json($matches);
    }

    // get macth by id
    public function show($match_id)
    {
        $match = StageMatch::with(['homeTeam.club', 'awayTeam.club', 'location', 'stage.tournament'])
            ->where('id', $match_id)
            ->first();

        try {
            if (!preg_match($this->urlRegex, $match->homeTeam->club->logo) && !str_contains($match->homeTeam->club->logo, 'assets/')) {
                $match->homeTeam->club->logo = $this->s3Instance->getObjectUrl($match->homeTeam->club->logo);
            }
        } catch (\Exception $e) {
            Log::error('Error fetching home team logo: ' . $e->getMessage());
        }

        try {
            if (!preg_match($this->urlRegex, $match->awayTeam->club->logo) && !str_contains($match->awayTeam->club->logo, 'assets/')) {
                $match->awayTeam->club->logo = $this->s3Instance->getObjectUrl($match->awayTeam->club->logo);
            }
        } catch (\Exception $e) {
            Log::error('Error fetching away team logo: ' . $e->getMessage());
        }


        return response()->json($match, 200);
    }

    // check match exists
    public function checkMatchExists($match_id)
    {
        $match = StageMatch::find($match_id);
        if ($match) {
            return response()->json(true, 200);
        }
        return response()->json(false, 200);
    }

    // get matches available for live stream
    public function getLiveMatches()
    {
        $timezone = request()->header('X-Time-Zone') ?? '+00:00';
        $newtimeZone = new DateTimeZone($timezone);

        $cancel_types = config('constants.cancel_match_types');
        $matches = StageMatch::with(['homeTeam.club', 'awayTeam.club', 'location', 'tournament'])
            ->whereHas('stage', function ($query) {
                $query->where('is_released', 1);
            })
            ->where(function ($query) use ($cancel_types) {
                $query->whereNotIn('status', $cancel_types)
                    ->orWhereNull('status');
            })
            ->whereNotNull('home_team_id')
            ->whereNotNull('away_team_id')
            ->where('end_time', '>=', Carbon::now()->setTimezone('UTC')->format('Y-m-d H:i:s'))
            ->where('end_time', '<=', Carbon::now()->addDay(1)->setTimezone('UTC')->format('Y-m-d H:i:s'))
            ->where('broadcast_status', '=', 'not_started')
            ->orderBy('start_time', 'asc')
            ->get();

        // Thêm các cột start_time_short và end_time_short
        $matches->transform(function ($match) use ($newtimeZone) {
            $match->start_time_short = is_null($match->start_time) || ($match->start_time == $match->end_time)
                ? 'TBD'
                : Carbon::parse($match->start_time, config('app.timezone'))->setTimezone($newtimeZone)->format('H:i');
            $match->end_time_short = is_null($match->end_time) || ($match->start_time == $match->end_time)
                ? 'TBD'
                : Carbon::parse($match->end_time, config('app.timezone'))->setTimezone($newtimeZone)->format('H:i');

            if (!preg_match($this->urlRegex, $match->homeTeam->club->logo) && !str_contains($match->homeTeam->club->logo, 'assets/')) {
                $match->homeTeam->club->logo = $this->s3Instance->getObjectUrl($match->homeTeam->club->logo);
            }
            if (!preg_match($this->urlRegex, $match->awayTeam->club->logo) && !str_contains($match->awayTeam->club->logo, 'assets/')) {
                $match->awayTeam->club->logo = $this->s3Instance->getObjectUrl($match->awayTeam->club->logo);
            }
            return $match;
        });
        return DataTables::of($matches)->make(true);
    }

    /**
     * Get streaming matches by status
     */
    public function getStreamingMatches($status = null)
    {
        $matches = StageMatch::with(['homeTeam.club', 'awayTeam.club', 'location', 'tournament']);

        // Log::info('status: ' . $status);

        if ($status !== null) {
            $matches->where('broadcast_status', $status);
        }

        // $matches = $matches->whereNotNull('broadcast_data')->get();
        $matches = $matches->orderBy('start_time', 'desc')->get();

        foreach ($matches as $match) {
            // Home team logo
            if (!preg_match($this->urlRegex, $match->homeTeam->club->logo) && !str_contains($match->homeTeam->club->logo, 'assets/')) {
                $match->homeTeam->club->logo = $this->s3Instance->getObjectUrl($match->homeTeam->club->logo);
            }

            // Away team logo
            if (!preg_match($this->urlRegex, $match->awayTeam->club->logo) && !str_contains($match->awayTeam->club->logo, 'assets/')) {
                $match->awayTeam->club->logo = $this->s3Instance->getObjectUrl($match->awayTeam->club->logo);
            }
        }
        return response()->json(['matches' => $matches]);
    }

    // update Broadcast id
    public function updateBroadcastId(Request $request)
    {
        $request->validate([
            'match_id' => 'required|integer',
            'broadcast_id' => 'required|string',
            'broadcast_status' => 'required|string'
        ]);
        $match = StageMatch::find($request->match_id);
        if ($match) {
            $match->broadcast_id = $request->broadcast_id;
            $match->broadcast_status = $request->broadcast_status;
            $match->save();
            return response()->json(true, 200);
        }
        return response()->json(false, 200);
    }

    // update Score
    public function updateScore(Request $request)
    {
        $request->validate([
            'match_id' => 'required|integer',
            'home_score' => 'required|integer',
            'away_score' => 'required|integer'
        ]);
        $match = StageMatch::find($request->match_id);
        if ($match) {
            $match->home_score = $request->home_score;
            $match->away_score = $request->away_score;
            $match->save();
            return response()->json(true, 200);
        }
        return response()->json(false, 200);
    }

    // update broadcast status
    public function updateBroadcastStatus(Request $request)
    {
        $request->validate([
            'match_id' => 'required|integer',
            'broadcast_status' => 'required|string'
        ]);
        $match = StageMatch::find($request->match_id);
        if ($match) {
            $match->broadcast_status = $request->broadcast_status;
            $match->save();
            return response()->json(true, 200);
        }
        return response()->json(false, 200);
    }

    // update broadcast data
    public function updateBroadcastData(Request $request)
    {
        $request->validate([
            'match_id' => 'required|integer',
            'broadcast_data' => 'required|array'
        ]);
        $match = StageMatch::find($request->match_id);
        if ($match) {
            $match->broadcast_data = $request->broadcast_data;
            $match->save();
            return response()->json(true, 200);
        }
        return response()->json(false, 200);
    }

    public function getUserLiveMatches($user_id)
    {
        $timezone = request()->header('X-Time-Zone') ?? '+00:00';
        $newtimeZone = new DateTimeZone($timezone);

        $cancel_types = config('constants.cancel_match_types');

        $matches = StageMatch::with(['homeTeam.club', 'awayTeam.club', 'location', 'tournament', 'userMatchStreamings.user'])
            ->whereHas('userMatchStreamings', function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            })
            ->whereHas('stage', function ($query) {
                $query->where('is_released', 1);
            })
            ->where(function ($query) use ($cancel_types) {
                $query->whereNotIn('status', $cancel_types)
                    ->orWhereNull('status');
            })
            ->whereNotNull('home_team_id')
            ->whereNotNull('away_team_id')
            ->where('end_time', '>=', Carbon::now()->setTimezone('UTC')->format('Y-m-d H:i:s'))
            ->where('end_time', '<=', Carbon::now()->addDay(1)->setTimezone('UTC')->format('Y-m-d H:i:s'))
            ->whereIn('broadcast_status', ['not_started', 'in_progress'])
            ->orderBy('start_time', 'asc')
            ->get();

        $matches->transform(function ($match) use ($newtimeZone) {
            $setting = Settings::where('key', 'name_settings')->first();
            $isOn = data_get($setting, 'value.is_on', 0);
            $cameraUser = $match->userMatchStreamings->firstWhere('role', 'camera');
            $controllerUser = $match->userMatchStreamings->firstWhere('role', 'control');
            $streamerUser = $match->userMatchStreamings->firstWhere('role', 'streamer');

            if ($streamerUser) {
                $name = $isOn ? $streamerUser->user->first_name . ' ' . $streamerUser->user->last_name : $streamerUser->user->last_name . ' ' . $streamerUser->user->first_name;
                $match->camera_name = $name;
                $match->controller_name = $name;
            } else {
                $match->camera_name = $cameraUser ? ($isOn ? $cameraUser->user->first_name . ' ' . $cameraUser->user->last_name : $cameraUser->user->last_name . ' ' . $cameraUser->user->first_name): '';
                $match->controller_name = $controllerUser ? ($isOn ? $controllerUser->user->first_name . ' ' . $controllerUser->user->last_name : $controllerUser->user->last_name . ' ' . $controllerUser->user->first_name): '';
            }
            $match->start_time_short = is_null($match->start_time) || ($match->start_time == $match->end_time)
                ? 'TBD'
                : Carbon::parse($match->start_time, config('app.timezone'))->setTimezone($newtimeZone)->format('H:i');
            $match->end_time_short = is_null($match->end_time) || ($match->start_time == $match->end_time)
                ? 'TBD'
                : Carbon::parse($match->end_time, config('app.timezone'))->setTimezone($newtimeZone)->format('H:i');

            return $match;
        });

        return response()->json([
            'success' => true,
            'data' => $matches,
        ]);
    }

    public function getRankings($stage_id)
    {
        $rankings = Rankings::where('stage_id', $stage_id)
            ->get();
        Log::info('rankings: ', [$rankings]);
        if ($rankings->isEmpty()) {
            return response()->json(['message' => __('No rankings found for the given stage_id')], 404);
        }

        return response()->json($rankings, 200);
    }

    public function getKnockoutGroupMatch($tournament_id)
    {
        $timezone = request()->header('X-Time-Zone') ?? '+00:00';
        $newtimeZone = new DateTimeZone($timezone);
        $matches = Stage::where('tournament_id', $tournament_id)
            ->with(['matches.homeLabel', 'matches.awayLabel'])
            ->get()
            ->pluck('matches')
            ->flatten();

        return DataTables::of($matches)
            ->with([
                'recordsTotal' => count($matches),
                'recordsFiltered' => count($matches)
            ])
            ->addColumn('start_time_short', function ($match) use ($newtimeZone) {
                $time_short = Carbon::parse($match->start_time, config('app.timezone'))->setTimezone($newtimeZone);
                return is_null($match->start_time) || ($match->start_time == $match->end_time) ? 'TBD' : $time_short->format('H:i');
            })
            ->addColumn('end_time_short', function ($match) use ($newtimeZone) {
                $time_short = Carbon::parse($match->end_time, config('app.timezone'))->setTimezone($newtimeZone);
                // Log::info('end time - start time: ', [$match->end_time, $match->start_time, is_null($match->start_time) || ($match->start_time == $match->end_time)]);
                return is_null($match->end_time) || ($match->start_time == $match->end_time) ? 'TBD' : $time_short->format('H:i');
            })
            ->addColumn('location_name', function ($match) {
                return is_null($match->location) ? 'TBD' : $match->location->name;
            })
            ->addColumn('home_team_name', function ($match) {
                return is_null($match->homeTeam) ? 'TBD' : $match->homeTeam->name;
            })
            ->addColumn('away_team_name', function ($match) {
                return is_null($match->awayTeam) ? 'TBD' : $match->awayTeam->name;
            })
            ->editColumn('home_score', function ($match) {
                return is_null($match->home_score) ? '' : $match->home_score;
            })
            ->editColumn('away_score', function ($match) {
                return is_null($match->away_score) ? '' : $match->away_score;
            })
            ->addColumn('date', function ($match) use ($newtimeZone) {
                if (is_null($match->start_time)) {
                    if (is_null($match->end_time)) {
                        return 'TBD';
                    } else {
                        $date = Carbon::parse($match->end_time, config('app.timezone'))->setTimezone($newtimeZone);
                        return date('Y-m-d', strtotime($date));
                    }
                } else {
                    $date = Carbon::parse($match->start_time, config('app.timezone'))->setTimezone($newtimeZone);
                    return date('Y-m-d', strtotime($date));
                }
            })
            ->make(true);
    }

    public function checkValidEditTime($stageMatchId, $currentTimeZone)
    {
        $stageMatchInfo = StageMatch::find($stageMatchId);
        $stageInfo = Stage::find($stageMatchInfo->stage_id);

        $numsOfLimitHour = $stageInfo->limit_update_score_time;

        if ($numsOfLimitHour == 0) {
            return [
                'success' => true,
                'message' => ''
            ];
        }

        $matchEndTime = new DateTime($stageMatchInfo->end_time, $currentTimeZone);
        $currentTime = new DateTime('now', $currentTimeZone);

        $limitTime = clone $matchEndTime;
        $limitTime->add(new DateInterval("PT{$numsOfLimitHour}H"));
        if ($currentTime < $limitTime) {
            return [
                'success' => true,
                'message' => ''
            ];
        }

        return [
            'success' => false,
            'message' => 'You can not update score after ' . $limitTime->format('Y-m-d H:i:s')
        ];
    }

    public function assignReferees(Request $request)
    {
        try {

            $request->validate([
                "list_match_ids" => "array|required",
                "list_referees" => "array",
            ]);

            foreach ($request->get("list_match_ids") as $matchId) {

                $currentStageMatchReferees = $this->stageMatchRefereeController->getCurrentRefereesByStageMatchId($matchId);

                $requestReferee = collect($request->get('list_referees'));

                ScheduleMatch::where('match_id', $matchId)->update([
                    'referee_ids' => $request->get('list_referees')
                ]);

                $removeIds = $currentStageMatchReferees['onlyIds']->diff($requestReferee);
                $addIds = $requestReferee->diff($currentStageMatchReferees['onlyIds']);

                Log::info("removeIds", [$removeIds]);
                Log::info("addIds", [$addIds]);

                foreach ($addIds as $refereeId) {
                    $this->stageMatchRefereeController->assignRefereeToStageMatch($matchId, $refereeId);
                }

                foreach ($removeIds as $refereeId) {
                    $this->stageMatchRefereeController->removeRefereeFromStageMatch($matchId, $refereeId);
                }
            }

            return response()->json([
                "message" => "Referees assigned successfully"
            ], 200);

        } catch (Exception $error) {
            return response()->json([
                "message" => $error->getMessage()
            ], 500);
        }
    }

    public function updateMatchOrder(Request $request)
    {
        $validatedData = $request->validate([
            '*.id' => 'required|integer|exists:stage_matches,id',
            '*.match_number' => 'required|integer',
            '*.round_level' => 'required|string',
            '*.stage_id' => 'required|integer',
        ]);

        // 1. Check if all matches are of the same type (group or knockout)
        $types = collect($validatedData)->map(function ($match) {
            return is_numeric($match['round_level']) ? 'knockout' : 'group';
        });

        if ($types->unique()->count() > 1) {
            return response()->json([
                'message' => 'All matches must be of the same type (group or knockout)',
            ], 422);
        }

        // 2. If it is a knockout, check the correct knockout order
        if ($types->first() === 'knockout') {
            $matchesGroupedByStage = collect($validatedData)->groupBy('stage_id');

            foreach ($matchesGroupedByStage as $stageId => $matches) {
                $sortedMatches = collect($matches)
                    ->sortBy('match_number')
                    ->values();

                $priorities = $sortedMatches->map(function ($match) {
                    $level = intval($match['round_level']);
                    return $level < 0 ? -1 : intval($level / 100);
                });

                for ($i = 0; $i < $priorities->count() - 1; $i++) {
                    $curr = $priorities[$i];
                    $next = $priorities[$i + 1];
                    if ($curr === -1 && $next === 1) {
                        continue;
                    }
                    if ($curr !== $next && $curr < $next) {
                        return response()->json([
                            'message' => "The match order is not allowed in knockout format.",
                        ], 422);
                    }
                }
            }
        }

        // 3. Update match_number
        foreach ($validatedData as $matchData) {
            $match = StageMatch::find($matchData['id']);
            $match->match_number = $matchData['match_number'];
            $match->save();
        }

        // 4. Update home_text, away_text for knockout matches
        if ($types->first() === 'knockout') {
            $matchesGroupedByStage = collect($validatedData)->groupBy('stage_id');

            foreach ($matchesGroupedByStage as $stageId => $matchesData) {
                $matches = StageMatch::where('stage_id', $stageId)->get();

                foreach ($matches as $match) {
                    $round_level = intval($match->round_level);
                    $match_number = $match->match_number;

                    $round = intval($round_level / 100);
                    $layer = $round_level % 100;
                    $next_level = ($round - 1) * 100 + ceil($layer / 2);

                    $next_match = $matches->where('round_level', $next_level)->first();

                    if ($next_match) {
                        if ($layer % 2 == 1) {
                            $next_match->home_text = "Winner of match $match_number";
                        } else {
                            $next_match->away_text = "Winner of match $match_number";
                        }
                        $next_match->save();
                    }

                    $next_level_loser = -$next_level;
                    $loser_match = $matches->where('round_level', $next_level_loser)->first();

                    if ($loser_match) {
                        if ($layer % 2 == 1) {
                            $loser_match->home_text = "Loser of match $match_number";
                        } else {
                            $loser_match->away_text = "Loser of match $match_number";
                        }
                        $loser_match->save();
                    }
                }
            }
        }

        return response()->json(['message' => 'Match numbers and references updated successfully'], 200);
    }

}
