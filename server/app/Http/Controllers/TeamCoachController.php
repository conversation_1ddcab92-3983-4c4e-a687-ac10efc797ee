<?php

namespace App\Http\Controllers;

use App\DataTables\TeamCoachesDataTableEditor;
use App\Models\Team;
use App\Models\TeamCoach;
use App\Models\User;
use App\Rules\Rules\Name;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TeamCoachController extends Controller
{
    // editor
    public function editor(TeamCoachesDataTableEditor $editor)
    {
        $action = request()->get('action');
        $params = request()->all();

        $data = $params['data'];
        $data = reset($data);
        $team_id = $data['team_id'];
        $user_id = $data['user_id'];
        // Process the request
        $result = $editor->process(request());

        // Check if the action is 'create' and notify the new coach
        if ($action == 'create') {
            $this->notifyNewCoach($user_id, $team_id);
        }
        return $result;
    }
    /**
     * Create a new coach and assign to team
     */
    public function assignNewCoach(Request $request)
    {
        $teamId = $request->team_id;
        // validate request
        $request->validate([
            'first_name' => ['required', 'string', new Name()],
            'last_name' => ['required', 'string', new Name()],
            'email' => 'required|email',
            'phone' => 'sometimes|nullable',
            'team_id' => 'required',
        ]);

        // check if user already exists
        $user = User::where('email', $request->input('email'))->first();

        // get user role of team coach from app.php config file

        // create user if not exists
        if (!$user) {
            $password = Str::random(5);

            $user = new User();
            $user->first_name = $request->first_name;
            $user->last_name = $request->last_name;
            $user->email = $request->email;
            $user->role_id = config('app.roles.team_coach');
            $user->password = Hash::make($password);
            // TODO: send email to coach with password
        }

        // check if coach is already assigned to team
        $teamCoach = TeamCoach::where('team_id', $teamId)->where('user_id', $user->id)->first();

        if ($teamCoach) {
            return response()->json([
                'message' => 'Coach is already assigned to team',
                'errors' => [
                    'email' => ['Coach is already assigned to team']
                ],
                'coach' => $user,
                'team_id' => $teamId,
            ], 422);
        } else {
            $user->phone = $request->phone ? $request->phone : $user->phone;
            $user->save();
            $teamCoach = new TeamCoach();
            $teamCoach->team_id = $teamId;
            $teamCoach->user_id = $user->id;
            $teamCoach->save();

            $this->notifyNewCoach($user->id, $teamId);
            return response()->json([
                'message' => 'Coach created and assigned to team successfully',
                'coach' => $user,
                'team_id' => $teamId,
            ], 201);
        }
    }
    public function notifyNewCoach($user_id, $team_id)
    {
        $team = Team::find($team_id);
        if (!$team) {
            return response()->json(['message' => __('Team not found')], 404);
        }

        $user_id_str = (string) $user_id;
        $request = new Request();
        $request->merge([
            'title' => 'New Assignment',
            'content' => 'You have been assigned as a coach to the team ' . $team->name,
            'user_ids' => $user_id_str,
            'type' => 'email_push_noti',
        ]);
        $send_mess_ctrl = new SendMessageController();
        $send_mess_ctrl->sendCustomMessage($request);

        return response()->json([
            'message' => __("Notification sent to the new coach"),
        ], 200);
    }
}
