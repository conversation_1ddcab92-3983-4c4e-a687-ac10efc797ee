<?php

namespace App\Http\Controllers;

use App\DataTables\TeamSheetsDataTableEditor;
use App\Models\RolePermission;
use App\Models\Settings;
use App\Models\Team;
use App\Models\TeamCoach;
use App\Models\TeamPlayer;
use App\Models\Teamsheet;
use App\Models\UserClub;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;
use MPDF;

class TeamsheetController extends Controller
{
    private $playerController;
    private $s3Controller;
    private $urlRegex;
    public function __construct()
    {
        $this->playerController = new PlayerController();
        $this->s3Controller = new S3Controller();
        $this->urlRegex = config("constants.regex.url", null);

    }

    //get all teamsheets
    public function all()
    {

        $teamsheets = Teamsheet::with(['team.club', 'team.group.season'])
            ->whereHas('team.club.userClubs', function ($query) {
                $hasPermission = RolePermission::where('role_id', Auth::user()->role_id)
                    ->where('permission_id', operator: 24)
                    ->exists();
                // if user had permission teamsheet manager, get all teams
                if ($hasPermission) {
                    return;
                }
                $query->where('user_id', Auth::user()->id);
            })
            ->orderBy('created_at', 'desc')->get();


        return DataTables::of($teamsheets)->make(true);
    }

    public function getTeamsheetBySeason($season_id)
    {
        $data = Teamsheet::with(['team.club', 'team.group.season']);

        $hasPermission = RolePermission::where('role_id', Auth::user()->role_id)
            ->where('permission_id', operator: 24)
            ->exists();

        // Check if you have permissions to team_coach or club_manager
        if (Auth::user()->role_id != 2){
            if ($hasPermission && (TeamCoach::where('user_id', Auth::id())->exists() || UserClub::where('user_id', Auth::id())->exists())) {
                // get teams for Club Manager
                $data->orWhereHas('team.club.userClubs', function ($query) {
                    $query->where('user_id', Auth::id());
                });
                // get teams for Team Coach
                $data->orWhereHas('team.teamCoaches', function ($query) {
                    $query->where('user_id', Auth::id());
                });
            }
        }

        $data->whereHas('team.group', function ($query) use ($season_id) {
            $query->where('season_id', $season_id);
        })
            ->orderBy('created_at', 'desc');

        $teamsheets = $data->get();

        foreach ($teamsheets as $teamsheet) {
            if (!preg_match($this->urlRegex, $teamsheet->document)) {
                $teamsheet->document = $this->s3Controller->getObjectUrl($teamsheet->document);
            }
        }

        return DataTables::of($teamsheets)->make(true);
    }


    public function editor(TeamSheetsDataTableEditor $editor)
    {
        return $editor->process(request());
    }

    // get teamsheet by team id
    public function getTeamsheetByTeamId($team_id)
    {
        $teamsheets = Teamsheet::with(['team.club', 'team.group.season'])->where('team_id', $team_id)->orderBy('created_at', 'desc')->get();

        foreach ($teamsheets as $teamsheet) {
            if (!preg_match($this->urlRegex, $teamsheet->document)) {
                $teamsheet->document = $this->s3Controller->getObjectUrl($teamsheet->document);
            }
        }

        return DataTables::of($teamsheets)->make(true);
    }

    // submit teamsheet
    public function submitTeamsheet(Request $request)
    {
        $request->validate([
            'team_id' => 'required|exists:teams,id',
        ]);

        // find team sheet by team id status LOCKED
        $teamSheet = Teamsheet::where('team_id', $request->team_id)->where('is_locked', 1)->first();
        if ($teamSheet) {
            return response()->json(['message' => 'Teamsheet is locked'], 400);
        }

        // get team players by team id
        $teamData = Team::with(['teamPlayers.player.user', 'group.season'])->where('id', $request->team_id)->first();

        foreach ($teamData->teamPlayers as $key => $item) {
            $player = $item->player;
            $teamData->teamPlayers[$key]['player'] = $this->playerController->mapPlayerImage([$player])[0];
        }

        $setting = Settings::where('key', 'name_settings')->first();
        $isOn = data_get($setting, 'value.is_on', 0);
        // if team has no players
        if ($teamData->teamPlayers->count() == 0) {
            return response()->json(['message' => 'Team has no players'], 400);
        }

        $clubInfo = $teamData->club;

        if (!preg_match($this->urlRegex, $clubInfo->logo)) {
            $clubInfo->logo = $this->s3Controller->getObjectUrl($clubInfo->logo);
        }

        $data = [
            'team' => $teamData,
            'team_players' => $teamData->teamPlayers->sortBy('player.user.last_name'),
            'club' => $clubInfo,
            'group' => $teamData->group,
            'season' => $teamData->group->season,
            'app_name' => config('app.name'),
        ];

        $data['is_on'] = $isOn;


//        return response()->json('test', 200);

        $pdf = Pdf::loadView('pdf.teamsheet', $data);
        $fileName = "teamsheets/". 'teamsheet_' . $request->team_id . '_' . time() . '.pdf';


        $uploadFileStatus = $this->s3Controller->putPDFObject("$fileName", $pdf->output());

        if (!$uploadFileStatus) {
            return response()->json(['message' => 'Something wrong when create Team Sheet file'], 500);
        }

        // save teamsheet to database
        $teamSheet = Teamsheet::create([
            'team_id' => $request->team_id,
            'document' => $fileName,
            'is_locked' => 0,
        ]);

        $responseData = [
            'teamsheet' => $teamSheet,
            'file_name' => $fileName,
        ];

        return response()->json($responseData, 200);
    }

    // show teamsheet
    public function show($id)
    {
        $data_team = Team::with(['teamPlayers.player.user', 'group.season'])->where('id', $id)->first();
        $data = [
            'team' => $data_team,
            'team_players' => $data_team->teamPlayers->sortBy('player.user.first_name'),
            'club' => $data_team->club,
            'group' => $data_team->group,
            'season' => $data_team->group->season,
            'app_name' => config('app.name'),
        ];
        return response()->json($data, 200);
    }
}
