<?php

namespace App\Http\Controllers;

use App\DataTables\TournamentsDataTableEditor;
use App\Models\Group;
use App\Models\RolePermission;
use App\Models\Season;
use App\Models\Stage;
use App\Models\StageMatch;
use App\Models\StageTeam;
use App\Models\Team;
use App\Models\TeamCoach;
use App\Models\Tournament;
use App\Models\UserClub;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class TournamentController extends Controller
{

    private $urlRegex;
    private $s3Instance;

    public function __construct()
    {
        $this->urlRegex = config("constants.regex.url", null);
        $this->s3Instance = new S3Controller();
    }

    //get all tournamets
    public function getAllTournaments()
    {
        $tournaments = Tournament::with('group')
            ->orderByRaw('CAST(SUBSTRING(name, 2) AS UNSIGNED)')
            ->orderBy('name', 'asc')
            ->get();

        return DataTables::of($tournaments)->make(true);
    }

    //all tournaments in group by group id
    public function allInGroup(Request $request)
    {
        $group_id = $request->group_id;
        $season_id = $request->season_id;
        $tournaments = Tournament::with(['stages', 'group']);
        if ($group_id) {
            Log::info('group_id: ' . $group_id);
            $tournaments = $tournaments->where('group_id', $group_id);
        } else if ($season_id) {
            $tournaments = $tournaments->whereHas('group', function ($query) use ($season_id) {
                $query->where('season_id', $season_id);
            });
        }
        // order by group name
        $tournaments = $tournaments->get();

        // Sort tournaments by `name` first
        $tournaments = $tournaments->sortBy(function ($tournament) {
            $name = $tournament['name'];
            $matches = [];

            // Separate the numbers and letters in the tournament name
            preg_match('/(\d+)/', $name, $matches);
            $number = count($matches) > 0 ? intval($matches[0]) : 0;
            $stringPart = preg_replace('/\d+/', '', $name);
            return [$number, $stringPart];
        })->values();

        $tournaments = $tournaments->sortBy(function ($tournament) {
            $name = $tournament->group['name'];
            $matches = [];

            // Separate numbers and letters in the group name
            preg_match('/(\d+)/', $name, $matches);
            $number = count($matches) > 0 ? intval($matches[0]) : 0;
            $stringPart = preg_replace('/\d+/', '', $name);
            return [$number, $stringPart];
        })->values();

        return DataTables::of($tournaments)
            ->make(true);
    }

    private function findSeasonIdByTournamentId($tournamentId)
    {
        $tournamentInfo = Tournament::find($tournamentId);

        if (!$tournamentInfo) {
            Log::error("Tournament not found for ID: $tournamentId");
            return null;
        }

        $groupInfo = Group::find($tournamentInfo->group_id);

        if (!$groupInfo) {
            Log::error("Group not found for ID: " . $tournamentInfo->group_id);
            return null;
        }

        Log::info("groupInfo", [$groupInfo]);

        return $groupInfo->season_id ?? null;
    }

    public function editor(TournamentsDataTableEditor $editor)
    {

        $process = $editor->process(request());

        $processData = $process->getData();


        if ($processData->action == 'create') {
            $seasonId = $this->findSeasonIdByTournamentId($processData->data[0]->id);
            $seasonInfo = Season::find($seasonId);

            $listStage = Stage::where('tournament_id', '=', $processData->data[0]->id)->get();

            foreach ($listStage as $stage) {
                $stage->limit_update_score_time = $seasonInfo->default_limit_update_score_time;
                $stage->save();
            }
        }

        return $process;
    }

    // get all matches by stage id
    public function getMatchesByStageId(Request $request)
    {
        $matches = DB::table('stage_matches')
            ->leftjoin('locations', 'stage_matches.location_id', '=', 'locations.id')
            ->where('stage_matches.stage_id', $request->stage_id)
            ->select('stage_matches.*', 'locations.name as location')
            ->get();

        foreach ($matches as $match) {
            // get date from start_time, if start_time is null then return TBD
            $match->date = is_null($match->start_time) ? 'TBD' : date('Y-m-d', strtotime($match->start_time));
            // format start_time, if start_time is null then return TBD
            $match->start_time = is_null($match->start_time) ? 'TBD' : date('H:i', strtotime($match->start_time));
            // format end_time, if end_time is null then return TBD
            $match->end_time = is_null($match->end_time) ? 'TBD' : date('H:i', strtotime($match->end_time));
            // get home_team name by home_team_id, if home_team_id is null then return TBD
            $home_team = DB::table('teams')->where('id', $match->home_team_id)->first();
            $match->home_team_id = is_null($home_team) ? 'TBD' : $home_team->name;
            // get away_team name by away_team_id, if away_team_id is null then return TBD
            $home_team = DB::table('teams')->where('id', $match->away_team_id)->first();
            $match->away_team_id = is_null($home_team) ? 'TBD' : $home_team->name;
            // get user (first_name + last_name) by scores_updated_by, if scores_updated_by is null then return ''
            $user = DB::table('users')->where('id', '=', $match->scores_updated_by)->first();
            $match->user = is_null($user) ? '' : $user->first_name . ' ' . $user->last_name;
        }

        return DataTables::of($matches)
            ->make(true);
    }

    public function getTournamentById($id)
    {
        $tournament = Tournament::with('group.season', 'stages')->find($id);
        // get group name by group_id
        $group = Group::find($tournament->group_id);
        $tournament->group_name = $group->name;
        // get stages by tournament_id

        return response()->json($tournament, 200);
    }

    public function showMatchesInSeasonByUser($season_id)
    {
        // get timezone from header X-Time-Zone
        $timezone = request()->header('X-Time-Zone') ?? '+00:00';
        $group_id = request()->input('group_id') ?? null;
        $tournament_id = request()->input('tournament_id') ?? null;
        $team_clubs = Auth::user()->teamsClub;
        $teams_coach = Auth::user()->teamsCoach;
        $teams = $team_clubs->merge($teams_coach);

        $hasPermission = RolePermission::where('role_id', Auth::user()->role_id)
            ->where('permission_id', 32)
            ->exists();

        if ($hasPermission && (!in_array(Auth::user()->role_id, [3, 4]))) {
            $teams = Team::whereHas('stages', function ($query) use ($tournament_id) {
                $query->where('tournament_id', $tournament_id);
            })->get();
        }

        $team_ids = $teams->pluck('id');
        // DB::enableQueryLog();
        $query = DB::table('stage_matches')
            ->join('stages', 'stage_matches.stage_id', '=', 'stages.id')
            ->join('tournaments', 'stages.tournament_id', '=', 'tournaments.id')
            ->leftJoin('groups', 'tournaments.group_id', '=', 'groups.id')
            ->leftJoin('locations', 'stage_matches.location_id', '=', 'locations.id')
            ->leftJoin('teams as home_team', 'stage_matches.home_team_id', '=', 'home_team.id')
            ->leftJoin('teams as away_team', 'stage_matches.away_team_id', '=', 'away_team.id')
            ->leftJoin('clubs as club_home_team', 'home_team.club_id', '=', 'club_home_team.id')
            ->leftJoin('clubs as club_away_team', 'away_team.club_id', '=', 'club_away_team.id')
            ->where('groups.season_id', $season_id)
            ->where('stages.is_released', 1)
            ->where(function ($query) {
                $query
                    ->where('stages.is_display_tbd', 1)
                    ->orWhere(function ($q) {
                        $q->where('stages.is_display_tbd', 0)
                            ->whereNotNull('stage_matches.home_team_id')
                            ->whereNotNull('stage_matches.away_team_id')
                            ->whereNotNull('stage_matches.start_time')
                            ->whereNotNull('stage_matches.end_time')
                            ->whereNotNull('stage_matches.location_id');
                    });
            });


        if ($tournament_id) {
            $query->where('tournaments.id', $tournament_id);
        }
        if ($group_id) {
            $query->where('groups.id', $group_id);
        }

        $matches = $query
            ->where(function ($query) use ($team_ids) {
                $query->whereIn('stage_matches.home_team_id', $team_ids)
                    ->orWhereIn('stage_matches.away_team_id', $team_ids);
            })
            ->select(
                'stage_matches.*',
                'locations.name as location',
                'home_team.name as home_team',
                'away_team.name as away_team',
                'groups.name as group_name',
                'groups.id as group_id',
                'club_home_team.name as club_home_team',
                'club_away_team.name as club_away_team',
                'club_home_team.logo as club_home_team_logo',
                'club_away_team.logo as club_away_team_logo',
                'tournaments.name as tournament_name',
                'tournaments.id as tournament_id',
                'stages.name as stage_name',
                'stages.id as stage_id',
                'stages.type as stage_type'
            )
            ->selectRaw('DATE_FORMAT(CONVERT_TZ(stage_matches.start_time, "+00:00","' . $timezone . '"), "%Y-%m-%d") AS dayofdate')
            ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as date')
            ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as start_time')
            ->selectRaw('DATE_FORMAT(stage_matches.end_time, "%Y-%m-%dT%TZ") as end_time')
            ->selectRaw('SUBSTRING_INDEX(stage_matches.round_name, "-", 1) as round')
            ->selectRaw('CONCAT(tournaments.name, "-", tournaments.id) as tournament_group')
            ->orderBy('tournament_id', 'asc')
            ->orderBy('stage_matches.start_time', 'asc')
            ->get();

        Log::info("matches", [$matches]);


        foreach ($matches as $match) {
            try {
                if (!preg_match($this->urlRegex, $match->club_away_team_logo) && !str_contains($match->club_away_team_logo, 'assets/')) {
                    Log::info("club_away_team_logo", [$match->club_away_team_logo]);
                    $match->club_away_team_logo = $this->s3Instance->getObjectUrl($match->club_away_team_logo);
                }
            } catch (\Exception $e) {
                Log::error("Error getting club_away_team_logo: " . $e->getMessage());
            }

            try {
                if (!preg_match($this->urlRegex, $match->club_home_team_logo) && !str_contains($match->club_home_team_logo, 'assets/')) {
                    Log::info("club_home_team_logo", [$match->club_home_team_logo]);
                    $match->club_home_team_logo = $this->s3Instance->getObjectUrl($match->club_home_team_logo);
                }
            } catch (\Exception $e) {
                Log::error("Error getting club_home_team_logo: " . $e->getMessage());
            }
        };

        // get tournaments have in team_ids
        $tournaments = DB::table('tournaments')
            ->join('stages', 'tournaments.id', '=', 'stages.tournament_id')
            ->join('stage_matches', 'stages.id', '=', 'stage_matches.stage_id')
            ->leftjoin('groups', 'tournaments.group_id', '=', 'groups.id')
            ->where('stage_matches.id', '!=', null)
            ->where(function ($query) use ($team_ids) {
                $query->whereIn('stage_matches.home_team_id', $team_ids)
                    ->orWhereIn('stage_matches.away_team_id', $team_ids);
            })
            ->where('groups.season_id', $season_id)
            ->distinct()
            ->select('tournaments.id', 'tournaments.name')
            ->orderBy('tournaments.name', 'asc')
            ->get();

        // group matches by date and tournament_id
        $matches = $matches->groupBy(['dayofdate', 'stage_type', 'round']);
        return response()->json([
            'matches' => $matches,
            'options' => [
                'tournaments' => $tournaments,
            ],
        ], 200);
    }

    // get fixtures matches in season
    public function showFixturesResultsInSeason($season_id)
    {
        $tournament_id = request()->input('tournament_id') ?? null;
        $team_id = request()->input('team_id') ?? null;
        $is_results = request()->input('is_results') ?? null;
        $timezone = request()->header('X-Time-Zone') ?? '+00:00';

        $query = DB::table('stage_matches')
            ->join('stages', 'stage_matches.stage_id', '=', 'stages.id')
            ->join('tournaments', 'stages.tournament_id', '=', 'tournaments.id')
            ->leftjoin('groups', 'tournaments.group_id', '=', 'groups.id')
            ->leftjoin('locations', 'stage_matches.location_id', '=', 'locations.id')
            ->leftjoin('teams as home_team', 'stage_matches.home_team_id', '=', 'home_team.id')
            ->leftjoin('teams as away_team', 'stage_matches.away_team_id', '=', 'away_team.id')
            ->leftJoin('clubs as club_home_team', 'home_team.club_id', '=', 'club_home_team.id')
            ->leftJoin('clubs as club_away_team', 'away_team.club_id', '=', 'club_away_team.id')
            ->where('groups.season_id', $season_id)
            ->where(function ($query) {
                $query->where('stage_matches.status', '!=', 'pass')
                    ->orWhereNull('stage_matches.status');
            })
            ->where('stages.is_released', 1)
            // if stage.is_display_tbd = 0 then hide TBD matches
            ->where(function ($query) {
                $query->where('stages.is_display_tbd', 1)
                    ->orWhere(function ($query) {
                        $query->where('stages.is_display_tbd', 0)
                            ->whereNotNull('stage_matches.home_team_id')
                            ->whereNotNull('stage_matches.away_team_id')
                            ->whereNotNull('stage_matches.start_time')
                            ->whereNotNull('stage_matches.end_time')
                            ->whereNotNull('stage_matches.location_id');
                    });
            });
        if ($is_results) {
            $query->where(function ($query) {
                $query->where('stage_matches.home_score', '!=', null)
                    ->orWhere('stage_matches.away_score', '!=', null);
            });
        } else {
            $query
                // And home_score is null or away_score is null
                ->where(function ($query) {
                    $query->where('stage_matches.home_score', '=', null)
                        ->orWhere('stage_matches.away_score', '=', null);
                });
            // where start_time is future or now or TBD
            // ->where(function ($query) {
            //     $query->where('stage_matches.start_time', '>=', Carbon::now())
            //         ->orWhere('stage_matches.start_time', '=', null);
            // });
        }
        if ($tournament_id) {
            $query->where('tournaments.id', $tournament_id);
        }

        if ($team_id) {
            $query->where(function ($query) use ($team_id) {
                $query->where('stage_matches.home_team_id', $team_id)
                    ->orWhere('stage_matches.away_team_id', $team_id);
            });
        }


        $matches = $query
            ->select(
                'stage_matches.*',
                'locations.name as location',
                'home_team.name as home_team',
                'away_team.name as away_team',
                'groups.name as group_name',
                'groups.id as group_id',
                'club_home_team.name as club_home_team',
                'club_away_team.name as club_away_team',
                'club_home_team.logo as club_home_team_logo',
                'club_away_team.logo as club_away_team_logo',
                'tournaments.name as tournament_name',
                'tournaments.id as tournament_id',
                'stages.name as stage_name',
                'stages.id as stage_id',
                'stages.type as stage_type'
            )
            ->selectRaw('DATE_FORMAT(CONVERT_TZ(stage_matches.start_time, "+00:00","' . $timezone . '"), "%Y-%m-%d") AS dayofdate')
            ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as date')
            ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as start_time')
            ->selectRaw('DATE_FORMAT(stage_matches.end_time, "%Y-%m-%dT%TZ") as end_time')
            ->selectRaw('SUBSTRING_INDEX(stage_matches.round_name, "-", 1) as round')
            ->selectRaw('CONCAT(tournaments.name, "-", tournaments.id) as tournament_group')
            ->orderBy('tournament_id', 'asc')
            ->orderBy('stage_matches.start_time', 'asc')
            ->get();

        // get stage_id group by stage_type, get one
        $stage_id = $matches->groupBy('stage_type')->map(function ($item) {
            return $item->first()->stage_id;
        });


        // group matches by date and tournament_id
        $matches = $matches->groupBy(['dayofdate', 'stage_type', 'round']);

        $data = [
            'matches' => $matches,
            'stage_id' => $stage_id,
        ];

        return response()->json($data, 200);
    }

    public function getTournamentBySeasonId(Request $request, $season_id)
    {
        $is_released = $request->is_released ? ($request->is_released == 'true' ? 1 : 0) : null;
        $is_scoreboard = $request->is_scoreboard ? ($request->is_scoreboard == 'true' ? 1 : 0) : null;

        $tournaments = Tournament::with(['group'])
            ->whereHas('group', function ($query) use ($season_id) {
                $query->where('season_id', $season_id);
            });

        if ($is_released != null) {
            $tournaments = $tournaments->whereHas('stages', function ($query) use ($is_released) {
                $query->where('is_released', $is_released);
            });
        }

        if ($is_scoreboard != null) {
            $tournaments = $tournaments->whereHas('stages', function ($query) use ($is_scoreboard) {
                $query->where('show_on_scoreboard', $is_scoreboard);
            });
        }

        $tournaments = $tournaments->orderByRaw('CAST(SUBSTRING(name, 2) AS UNSIGNED)')->orderBy('name', 'asc')->get();
        Log::info($tournaments);
        return DataTables::of($tournaments)
            ->with('options', [
                'groups' => $tournaments->pluck('group')->unique('id')->values(),
            ])->make(true);
    }

    public function allInGroupSortByOrder(Request $request)
    {
        $group_id = $request->group_id;
        $season_id = $request->season_id;
        $tournaments = Tournament::with(['stages', 'group']);
        if ($group_id) {
            Log::info('group_id: ' . $group_id);
            $tournaments = $tournaments->where('group_id', $group_id);
        } else if ($season_id) {
            $tournaments = $tournaments->whereHas('group', function ($query) use ($season_id) {
                $query->where('season_id', $season_id);
            });
        }

        // order by group name
        $tournaments = $tournaments->orderBy('order', 'asc')->orderByRaw('CAST(SUBSTRING(name, 2) AS UNSIGNED)')->orderBy('name', 'asc')->get();
        return DataTables::of($tournaments)
            ->make(true);
    }

    public function updateOrder(Request $request)
    {
        $tournaments = $request->input('tournaments');
        try {
            foreach ($tournaments as $tournament) {
                // update order for tournament
                Tournament::where('id', $tournament['id'])
                    ->update(['order' => $tournament['order']]);

                // update show_on_scoreboard for tournament
                if (isset($tournament['stages'])) {
                    foreach ($tournament['stages'] as $stage) {
                        Stage::where('id', $stage['id'])
                            ->update(['show_on_scoreboard' => $stage['show_on_scoreboard']]);
                    }
                }
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Something went wrong. Please contact admin!'], 500);
        }
    }


    // get select option tournaments in season
    public function optionsTournaments(Request $request, $season_id = 0, $group_id = 0)
    {
        $check_user = $request->check_user ?? 'false';
        $user_id = $request->user()->id ?? 0;

        // get tournaments with teams in season
        $tournaments = DB::table('tournaments')
            ->join('stages', 'tournaments.id', '=', 'stages.tournament_id')
            ->leftjoin('groups', 'tournaments.group_id', '=', 'groups.id')
            ->where('groups.season_id', $season_id)
            ->where('stages.is_released', 1)
            ->distinct()
            ->select('tournaments.id', 'tournaments.name', 'stages.type as stage_type')
            ->orderBy('tournaments.name', 'asc');

        // if group_id = 0 then get tournaments in season
        if ($group_id) {
            $tournaments->where('tournaments.group_id', $group_id);
        }

        // if request check_user is true and user is not admin then get only tournaments in season related to user
        if (Auth::user()->role_id != 2) {
            if ($check_user == 'true' && (TeamCoach::where('user_id', Auth::id())->exists() || UserClub::where('user_id', Auth::id())->exists())) {
                $tournaments->join('stage_teams', 'stages.id', '=', 'stage_teams.stage_id')
                    ->join('teams', 'teams.id', '=', 'stage_teams.team_id');
                $temp = clone $tournaments;
                $club_manager = $tournaments->join('user_clubs', function ($join) use ($user_id) {
                    $join->on('user_clubs.club_id', '=', 'teams.club_id')
                        ->where('user_clubs.user_id', '=', $user_id);
                })->get();

                $team_coach = $temp->join('team_coaches', function ($join) use ($user_id) {
                    $join->on('team_coaches.team_id', '=', 'teams.id')
                        ->where('team_coaches.user_id', '=', $user_id);
                })->get();

                // merge two collection
                $tournaments = $club_manager->merge($team_coach)->unique('id')
                    ->groupBy(fn($item) => $item->id . '|' . $item->name)
                    ->map(function ($items) {
                        $first = $items->first();
                        $first->stage_type = $items->pluck('stage_type')->unique()->implode(', ');
                        return $first;
                    })
                    ->values();
            } else {
                $tournaments = $tournaments->get()
                    ->groupBy(function ($item) {
                        return $item->id . '|' . $item->name;
                    })
                    ->map(function ($items) {
                        $first = $items->first();
                        $first->stage_type = $items->pluck('stage_type')->unique()->implode(', ');
                        return $first;
                    })
                    ->values();
            }
        } else {
            $tournaments = $tournaments->get()
                ->groupBy(function ($item) {
                    return $item->id . '|' . $item->name;
                })
                ->map(function ($items) {
                    $first = $items->first();
                    $first->stage_type = $items->pluck('stage_type')->unique()->implode(', ');
                    return $first;
                })
                ->values();
        }


        // get teams in tournaments
        foreach ($tournaments as $tournament) {
            $tournament->teams = DB::table('teams')
                ->join('stage_teams', 'teams.id', '=', 'stage_teams.team_id')
                ->join('stages', 'stage_teams.stage_id', '=', 'stages.id')
                ->join('tournaments', 'stages.tournament_id', '=', 'tournaments.id')
                ->where('tournaments.id', $tournament->id)
                ->select('teams.id', 'teams.name')
                ->orderBy('teams.name', 'asc')
                ->distinct()
                ->get();
        }

        // sort bu number in name, eg:  U9 -> Champ U9 -> U10 -> U11
        $tournaments = $tournaments->toArray();
        usort($tournaments, function ($a, $b) {
            $a = preg_replace('/\d+/', '', $a->name);
            $b = preg_replace('/\d+/', '', $b->name);
            return $a <=> $b;
        });

        return response()->json([
            'tournaments' => $tournaments,
        ], 200);
    }

    // get Knockout matches in tournament
    function knockOutMatches($tournament_id)
    {
        $matches = StageMatch::with(['homeTeam.club', 'awayTeam.club', 'location'])
            ->whereHas('stage', function ($query) use ($tournament_id) {
                $query->where('tournament_id', $tournament_id)
                    ->where('type', config('constants.tournament_types.knockout'));
            })->get();

        // group matches by round_name split by ' ', trim space. and group by round_name split by '-'.
        //  ex : ['Cup' => ['Cup - 1', 'Cup - 2'], 'Plate' => ['Plate - 1', 'Plate - 2']]
        $matches = $matches->groupBy(function ($item) {
            return trim(explode(' ', $item->round_name)[0]);
        })->map(function ($item) {
            return $item->groupBy(function ($item) {
                return trim(explode('-', $item->round_name)[0]);
            });
        });
        return response()->json($matches, 200);
    }

    public function fixturesResults($tournament_id)
    {
        $is_results = isset(request()->is_results) ? (request()->is_results == '1' ? 1 : 0) : null;
        $team_id = request()->input('team_id') ?? null;
        $timezone = request()->header('X-Time-Zone') ?? '+00:00';

        // get tournament by id
        $tournament = Tournament::find($tournament_id);
        $query = DB::table('stage_matches')
            ->join('stages', 'stage_matches.stage_id', '=', 'stages.id')
            ->join('tournaments', 'stages.tournament_id', '=', 'tournaments.id')
            ->leftjoin('groups', 'tournaments.group_id', '=', 'groups.id')
            ->leftjoin('locations', 'stage_matches.location_id', '=', 'locations.id')
            ->leftjoin('teams as home_team', 'stage_matches.home_team_id', '=', 'home_team.id')
            ->leftjoin('teams as away_team', 'stage_matches.away_team_id', '=', 'away_team.id')
            ->leftJoin('clubs as club_home_team', 'home_team.club_id', '=', 'club_home_team.id')
            ->leftJoin('clubs as club_away_team', 'away_team.club_id', '=', 'club_away_team.id')
            ->where('stages.is_released', 1)
            ->where(function ($query) {
                $query->where('stage_matches.status', '!=', 'pass')
                    ->orWhereNull('stage_matches.status');
            })

            // if stage.is_display_tbd = 0 then hide TBD matches
            ->where(function ($query) {
                $query->where('stages.is_display_tbd', 1)
                    ->orWhere(function ($query) {
                        $query->where('stages.is_display_tbd', 0)
                            ->whereNotNull('stage_matches.home_team_id')
                            ->whereNotNull('stage_matches.away_team_id')
                            ->whereNotNull('stage_matches.start_time')
                            ->whereNotNull('stage_matches.end_time')
                            ->whereNotNull('stage_matches.location_id');
                    });
            });

        if ($tournament_id) {
            $query = $query->where('tournaments.id', $tournament_id);
        }

        if ($team_id) {
            $query = $query->where(function ($query) use ($team_id) {
                $query->where('stage_matches.home_team_id', $team_id)
                    ->orWhere('stage_matches.away_team_id', $team_id);
            });
        }


        if ($is_results !== null) {
            if ($is_results === 1) {
                $query = $query
                    ->where(function ($query) {
                        $query->where('stage_matches.home_score', '!=', null)
                            ->orWhere('stage_matches.away_score', '!=', null);
                    })
                    ->where('stages.is_display_results', '=', 1)
                    ->orderBy('stage_matches.start_time', 'desc');
            } else if ($is_results === 0) {
                $query = $query
                    // And home_score is null or away_score is null
                    ->where(function ($query) {
                        $query->where('stage_matches.home_score', '=', null)
                            ->orWhere('stage_matches.away_score', '=', null);
                    })
                    ->orderBy('stage_matches.start_time', 'asc');
                // where start_time is future or now or TBD
                // ->where(function ($query) {
                //     $query->where('stage_matches.start_time', '>=', Carbon::now())
                //         ->orWhere('stage_matches.start_time', '=', null);
                // });
            }
        }

        $matches = $query
            ->select(
                'stage_matches.*',
                'locations.name as location',
                'home_team.name as home_team',
                'away_team.name as away_team',
                'groups.name as group_name',
                'groups.id as group_id',
                'club_home_team.name as club_home_team',
                'club_away_team.name as club_away_team',
                'club_home_team.logo as club_home_team_logo',
                'club_away_team.logo as club_away_team_logo',
                'tournaments.name as tournament_name',
                'tournaments.id as tournament_id',
                'stages.name as stage_name',
                'stages.id as stage_id',
                'stages.type as stage_type'
            )
            ->selectRaw('DATE_FORMAT(CONVERT_TZ(stage_matches.start_time, "+00:00","' . $timezone . '"), "%Y-%m-%d") AS dayofdate')
            ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as date')
            ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as start_time')
            ->selectRaw('DATE_FORMAT(stage_matches.end_time, "%Y-%m-%dT%TZ") as end_time')
            ->selectRaw('SUBSTRING_INDEX(stage_matches.round_name, "-", 1) as round')
            ->selectRaw('CONCAT(tournaments.name, "-", tournaments.id) as tournament_group')
            ->orderBy('tournament_id', 'asc')
            ->orderBy('stage_matches.start_time', 'asc')
            ->get();

        foreach ($matches as $match) {

            try {
                if (!preg_match($this->urlRegex, $match->club_away_team_logo) && !str_contains($match->club_away_team_logo, 'assets/')) {
                    $match->club_away_team_logo = $this->s3Instance->getObjectUrl($match->club_away_team_logo);
                }
            } catch (\Exception $e) {
                Log::error("Error getting club_away_team_logo: " . $e->getMessage());
            }

            try {
                if (!preg_match($this->urlRegex, $match->club_home_team_logo) && !str_contains($match->club_home_team_logo, 'assets/')) {
                    $match->club_home_team_logo = $this->s3Instance->getObjectUrl($match->club_home_team_logo);
                }
            } catch (\Exception $e) {
                Log::error("Error getting club_home_team_logo: " . $e->getMessage());
            }

        };
        // get list of stage_id group by stage_type
        $stage_id = $matches->groupBy('stage_id')->map(function ($item) {
            return $item->first()->stage_type;
        });

        Log::info($stage_id);


        // group matches by date and tournament_id
        $matches = $matches->groupBy(['dayofdate', 'stage_type', 'round']);

        $data = [
            'matches' => $matches,
            'stage_id' => $stage_id,
            'tournament' => $tournament,
        ];

        if ($team_id) {
            $team = Team::with(['club', 'group', 'players.user'])->find($team_id);
            $data['team'] = $team;
        }

        return response()->json($data, 200);
    }

    public function scoreboardMatches($tournament_id)
    {
        $timezone = request()->header('X-Time-Zone') ?? '+00:00';
        $number_of_matches = request()->input('number_of_matches') ?? 12;

        // get tournament by id
        $tournament = Tournament::find($tournament_id);

        // get fixtures matches
        $fixture_matches = DB::table('stage_matches')
            ->join('stages', 'stage_matches.stage_id', '=', 'stages.id')
            ->join('tournaments', 'stages.tournament_id', '=', 'tournaments.id')
            ->leftjoin('groups', 'tournaments.group_id', '=', 'groups.id')
            ->leftjoin('locations', 'stage_matches.location_id', '=', 'locations.id')
            ->leftjoin('teams as home_team', 'stage_matches.home_team_id', '=', 'home_team.id')
            ->leftjoin('teams as away_team', 'stage_matches.away_team_id', '=', 'away_team.id')
            ->leftJoin('clubs as club_home_team', 'home_team.club_id', '=', 'club_home_team.id')
            ->leftJoin('clubs as club_away_team', 'away_team.club_id', '=', 'club_away_team.id')
            ->where('stages.is_released', 1)
            ->where('stages.show_on_scoreboard', 1)
            ->where(function ($query) {
                $query->where('stage_matches.status', '!=', 'pass')
                    ->orWhereNull('stage_matches.status');
            })
            ->where(function ($query) {
                $query->where('stages.is_display_tbd', 1)
                    ->orWhere(function ($query) {
                        $query->where('stages.is_display_tbd', 0)
                            ->whereNotNull('stage_matches.home_team_id')
                            ->whereNotNull('stage_matches.away_team_id')
                            ->whereNotNull('stage_matches.start_time')
                            ->whereNotNull('stage_matches.end_time')
                            ->whereNotNull('stage_matches.location_id');
                    });
            })
            ->where('tournaments.id', $tournament_id)
            // And home_score is null or away_score is null
            ->where(function ($query) {
                $query->where('stage_matches.home_score', '=', null)
                    ->orWhere('stage_matches.away_score', '=', null);
            })
            ->select(
                'stage_matches.*',
                'locations.name as location',
                'home_team.name as home_team',
                'away_team.name as away_team',
                'groups.name as group_name',
                'groups.id as group_id',
                'club_home_team.name as club_home_team',
                'club_away_team.name as club_away_team',
                'club_home_team.logo as club_home_team_logo',
                'club_away_team.logo as club_away_team_logo',
                'tournaments.name as tournament_name',
                'tournaments.id as tournament_id',
                'stages.name as stage_name',
                'stages.id as stage_id',
                'stages.type as stage_type'
            )
            ->selectRaw('DATE_FORMAT(CONVERT_TZ(stage_matches.start_time, "+00:00","' . $timezone . '"), "%Y-%m-%d") AS dayofdate')
            ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as date')
            ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as start_time')
            ->selectRaw('DATE_FORMAT(stage_matches.end_time, "%Y-%m-%dT%TZ") as end_time')
            ->selectRaw('SUBSTRING_INDEX(stage_matches.round_name, "-", 1) as round')
            ->selectRaw('CONCAT(tournaments.name, "-", tournaments.id) as tournament_group')
            ->orderBy('stage_matches.start_time', 'asc')
            ->limit($number_of_matches)
            ->get();

        // set matches by fixture_matches
        $matches = $fixture_matches->sortBy('start_time');

        // if results less than number_of_matches then get fixtures matches
        if (count($fixture_matches) < $number_of_matches) {
            $limit = $number_of_matches - count($fixture_matches);
            $result_matches = DB::table('stage_matches')
                ->join('stages', 'stage_matches.stage_id', '=', 'stages.id')
                ->join('tournaments', 'stages.tournament_id', '=', 'tournaments.id')
                ->leftjoin('groups', 'tournaments.group_id', '=', 'groups.id')
                ->leftjoin('locations', 'stage_matches.location_id', '=', 'locations.id')
                ->leftjoin('teams as home_team', 'stage_matches.home_team_id', '=', 'home_team.id')
                ->leftjoin('teams as away_team', 'stage_matches.away_team_id', '=', 'away_team.id')
                ->leftJoin('clubs as club_home_team', 'home_team.club_id', '=', 'club_home_team.id')
                ->leftJoin('clubs as club_away_team', 'away_team.club_id', '=', 'club_away_team.id')
                ->where('stages.is_released', 1)
                ->where('stages.show_on_scoreboard', 1)
                ->where(function ($query) {
                    $query->where('stage_matches.status', '!=', 'pass')
                        ->orWhereNull('stage_matches.status');
                })
                ->where(function ($query) {
                    $query->where('stages.is_display_tbd', 1)
                        ->orWhere(function ($query) {
                            $query->where('stages.is_display_tbd', 0)
                                ->whereNotNull('stage_matches.home_team_id')
                                ->whereNotNull('stage_matches.away_team_id')
                                ->whereNotNull('stage_matches.start_time')
                                ->whereNotNull('stage_matches.end_time')
                                ->whereNotNull('stage_matches.location_id');
                        });
                })
                ->where('tournaments.id', $tournament_id)
                ->where(function ($query) {
                    $query->where('stage_matches.home_score', '!=', null)
                        ->orWhere('stage_matches.away_score', '!=', null);
                })
                ->where('stages.is_display_results', '=', 1)
                // start_time > 00:00:00 today
                ->where('stage_matches.start_time', '>', Carbon::today())
                ->select(
                    'stage_matches.*',
                    'locations.name as location',
                    'home_team.name as home_team',
                    'away_team.name as away_team',
                    'groups.name as group_name',
                    'groups.id as group_id',
                    'club_home_team.name as club_home_team',
                    'club_away_team.name as club_away_team',
                    'club_home_team.logo as club_home_team_logo',
                    'club_away_team.logo as club_away_team_logo',
                    'tournaments.name as tournament_name',
                    'tournaments.id as tournament_id',
                    'stages.name as stage_name',
                    'stages.id as stage_id',
                    'stages.type as stage_type'
                )
                ->selectRaw('DATE_FORMAT(CONVERT_TZ(stage_matches.start_time, "+00:00","' . $timezone . '"), "%Y-%m-%d") AS dayofdate')
                ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as date')
                ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as start_time')
                ->selectRaw('DATE_FORMAT(stage_matches.end_time, "%Y-%m-%dT%TZ") as end_time')
                ->selectRaw('SUBSTRING_INDEX(stage_matches.round_name, "-", 1) as round')
                ->selectRaw('CONCAT(tournaments.name, "-", tournaments.id) as tournament_group')
                ->orderBy('tournament_id', 'asc')
                ->orderBy('stage_matches.start_time', 'desc')
                ->limit($limit)
                ->get();

            // sort result_matches by start_time asc
            $result_matches = $result_matches->sortBy('start_time');

            // merge results matches to fixtures
            $matches = $result_matches->merge($matches);
        }

        foreach ($matches as $match) {

            try {
                if (!preg_match($this->urlRegex, $match->club_away_team_logo) && !str_contains($match->club_away_team_logo, 'assets/')) {
                    $match->club_away_team_logo = $this->s3Instance->getObjectUrl($match->club_away_team_logo);
                }
            } catch (\Exception $e) {
                Log::error("Error getting club_away_team_logo: " . $e->getMessage());
            }

            try {
                if (!preg_match($this->urlRegex, $match->club_home_team_logo) && !str_contains($match->club_home_team_logo, 'assets/')) {
                    $match->club_home_team_logo = $this->s3Instance->getObjectUrl($match->club_home_team_logo);
                }
            } catch (\Exception $e) {
                Log::error("Error getting club_home_team_logo: " . $e->getMessage());
            }
        };        // get list of stage_id group by stage_type

        // get stage_id group by stage_type, get one
        $stage_id = $matches->groupBy('stage_type')->map(function ($item) {
            return $item->first()->stage_id;
        });

        // group matches by date and tournament_id
        $matches = $matches->groupBy(['dayofdate', 'stage_type', 'round']);

        $data = [
            'matches' => $matches,
            'stage_id' => $stage_id,
            'tournament' => $tournament,
        ];

        return response()->json($data, 200);
    }

    public function fixturesResultsByTeam($team_id)
    {
        $timezone = request()->header('X-Time-Zone') ?? '+00:00';

        $query = DB::table('stage_matches')
            ->join('stages', 'stage_matches.stage_id', '=', 'stages.id')
            ->join('tournaments', 'stages.tournament_id', '=', 'tournaments.id')
            ->leftjoin('groups', 'tournaments.group_id', '=', 'groups.id')
            ->leftjoin('locations', 'stage_matches.location_id', '=', 'locations.id')
            ->leftjoin('teams as home_team', 'stage_matches.home_team_id', '=', 'home_team.id')
            ->leftjoin('teams as away_team', 'stage_matches.away_team_id', '=', 'away_team.id')
            ->leftJoin('clubs as club_home_team', 'home_team.club_id', '=', 'club_home_team.id')
            ->leftJoin('clubs as club_away_team', 'away_team.club_id', '=', 'club_away_team.id')
            ->where('stages.is_released', 1)
            ->where(function ($query) {
                $query->where('stage_matches.status', '!=', 'pass')
                    ->orWhereNull('stage_matches.status');
            })
            // if stage.is_display_tbd = 0 then hide TBD matches
            ->where(function ($query) {
                $query->where('stages.is_display_tbd', 1)
                    ->orWhere(function ($query) {
                        $query->where('stages.is_display_tbd', 0)
                            ->whereNotNull('stage_matches.home_team_id')
                            ->whereNotNull('stage_matches.away_team_id')
                            ->whereNotNull('stage_matches.start_time')
                            ->whereNotNull('stage_matches.end_time')
                            ->whereNotNull('stage_matches.location_id');
                    });
            });

        if ($team_id) {
            $query = $query->where(function ($query) use ($team_id) {
                $query->where('stage_matches.home_team_id', $team_id)
                    ->orWhere('stage_matches.away_team_id', $team_id);
            });
        }

        $matches = $query  // where start_time is future or now or TBD

        ->select(
            'stage_matches.*',
            'locations.name as location',
            'home_team.name as home_team',
            'away_team.name as away_team',
            'groups.name as group_name',
            'groups.id as group_id',
            'club_home_team.name as club_home_team',
            'club_away_team.name as club_away_team',
            'club_home_team.logo as club_home_team_logo',
            'club_away_team.logo as club_away_team_logo',
            'tournaments.name as tournament_name',
            'tournaments.id as tournament_id',
            'stages.name as stage_name',
            'stages.id as stage_id',
            'stages.type as stage_type'
        )
            ->selectRaw('DATE_FORMAT(CONVERT_TZ(stage_matches.start_time, "+00:00","' . $timezone . '"), "%Y-%m-%d") AS dayofdate')
            ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as date')
            ->selectRaw('DATE_FORMAT(stage_matches.start_time, "%Y-%m-%dT%TZ") as start_time')
            ->selectRaw('DATE_FORMAT(stage_matches.end_time, "%Y-%m-%dT%TZ") as end_time')
            ->selectRaw('SUBSTRING_INDEX(stage_matches.round_name, "-", 1) as round')
            ->selectRaw('CONCAT(tournaments.name, "-", tournaments.id) as tournament_group')
            ->orderBy('tournament_id', 'asc')
            ->orderBy('stage_matches.start_time', 'asc')
            ->get();

        // get stage_id group by stage_type, get one
        $stage_id = $matches->groupBy('stage_type')->map(function ($item) {
            return $item->first()->stage_id;
        });

        foreach ($matches as $match) {
            if (!preg_match($this->urlRegex, $match->club_away_team_logo) && !str_contains($match->club_away_team_logo, 'assets/')) {
                $match->club_away_team_logo = $this->s3Instance->getObjectUrl($match->club_away_team_logo);
            }
            if (!preg_match($this->urlRegex, $match->club_home_team_logo) && !str_contains($match->club_home_team_logo, 'assets/')) {
                $match->club_home_team_logo = $this->s3Instance->getObjectUrl($match->club_home_team_logo);
            }
        };

        // group matches by date and tournament_id
        $matches = $matches->groupBy(['dayofdate', 'stage_type', 'round']);
        // pluck tournament_group
        // $tournament_groups = $matches->keys();
        $data = [
            'matches' => $matches,
            'stage_id' => $stage_id,
        ];

        if ($team_id) {
            $teams = Team::with(['club', 'group', 'players.user'])->find($team_id);

            if (!preg_match($this->urlRegex, $teams->club->logo) && !str_contains($teams->club->logo, 'assets/')) {
                $teams->club->logo = $this->s3Instance->getObjectUrl($teams->club->logo);
            }

            foreach ($teams->players as $player) {
                Log::info("player", [$player]);
                if (!preg_match($this->urlRegex, $player->photo) && !str_contains($player->photo, 'assets/')) {
                    $player->photo = $this->s3Instance->getObjectUrl($player->photo);
                }
            };
            $data['team'] = $teams;
        }

        return response()->json($data, 200);
    }

    function getStagesByTournamentId($tournamentId)
    {
        $stages = Stage::where('tournament_id', $tournamentId)->get();
        return response()->json($stages, 200);
    }

    function getListGroupsByTournamentId($tournament_id)
    {
        try {

            $tournamentInfo = Tournament::find($tournament_id);
            $stage = Stage::where('tournament_id', $tournament_id);

            if ($tournamentInfo->type == config('constants.tournament_types.league')) {
                $stage = $stage->where('type', 'League')->first();
            } else {
                $stage = $stage->where('type', 'Groups')->first();
            }

            $stageTeams = StageTeam::where('stage_id', $stage->id)
                ->whereNotNull('group')
                ->pluck('group')
                ->unique()
                ->values()
                ->toArray();

            Log::info('$stageTeams', [$stageTeams]);

            return [
                'status' => 'success',
                'message' => 'List groups retrieved successfully',
                'data' => $stageTeams,
            ];

        } catch (Exception $error) {
            Log::error("Error in getListGroupsByStageId", [$error]);
            return response()->json([
                "message" => $error->getMessage(),
            ], 500);
        }
    }
}
