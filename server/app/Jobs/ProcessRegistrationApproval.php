<?php

namespace App\Jobs;

use App\Http\Controllers\RegistrationController;
use App\Models\Registration;
use App\Models\User;
use App\Models\Season;
use App\Models\PaymentDetail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ProcessRegistrationApproval implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Registration $registration;
    protected User $guardian;
    protected Season $season;
    protected User $player;
    protected $isPaymentRequired;
    protected $request_all;

    public function __construct(
        Registration $registration,
        User         $guardian,
        Season       $season,
        User         $player,
        bool         $isPaymentRequired,
                     $request_all
    )
    {
        $this->registration = $registration;
        $this->guardian = $guardian;
        $this->season = $season;
        $this->player = $player;
        $this->isPaymentRequired = $isPaymentRequired;
        $this->request_all = $request_all;
    }

    public function handle()
    {
        try {
            $controller = new RegistrationController();
            $request = new Request($this->request_all);

            // has user_id = approver_id

            $resInvoice = $controller->createInvoiceApprove($this->registration, $this->guardian, $request, $this->isPaymentRequired);
            if ($resInvoice->status() == 400) {
                // handle failed update status
                $this->registration->updateApproveStatus($this->registration->id, config('constants.approve_status.payment_error'));
            } else if ($resInvoice->status() == 200 || $resInvoice->status() == 201) {
                $this->registration->updateApproveStatus($this->registration->id, config('constants.approve_status.approved'));
            }

        } catch (\Exception $e) {
        }
    }

}
