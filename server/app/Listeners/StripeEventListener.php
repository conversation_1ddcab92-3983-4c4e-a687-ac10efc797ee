<?php

namespace App\Listeners;

use App\Models\Payment;
use App\Models\PaymentDetail;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Cashier\Events\WebhookReceived;

class StripeEventListener
{
    /**
     * <PERSON><PERSON> received Stripe webhooks.
     */
    public function handle(WebhookReceived $event): void
    {
        Log::info('StripeEventListener', ['payload' => $event->payload]);
        $object = $event->payload['data']['object'];
        //check type contains 'succeeded'
        switch ($event->payload['type']) {
            case 'invoice.payment_succeeded':
                Log::info('payment succeeded', ['payment_intent' => $object['payment_intent']]);
                // find payment_intent in table payment and update status
                $payment = Payment::where('payment_intent', $object['payment_intent'])->first();
                if ($payment) {
                    $payment->status = $object['status'];
                    $payment->save();
                    // find payment details in table payment_details and update status
                    PaymentDetail::where('payment_id', $payment->id)->update(['status' => $object['status']]);
                }
                break;
            case 'invoice.payment_failed':
                Log::info('payment failed', ['payment_intent' => $object['payment_intent']]);
                $payment = Payment::where('payment_intent', $object['payment_intent'])->first();
                if ($payment) {
                    $payment->status = $object['status'];
                    $payment->save();
                    // find payment details in table payment_details and update status
                    PaymentDetail::where('payment_id', $payment->id)->update(['status' => $object['status']]);
                }
                break;
        }
    }
}
