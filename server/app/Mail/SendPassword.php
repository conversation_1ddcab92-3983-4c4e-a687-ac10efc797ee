<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\HtmlString;
use App\Models\EmailTemplate;
use Illuminate\Support\Facades\Log;

class SendPassword extends Notification
{
    /**
     * The password reset token.
     *
     * @var string
     */
    public $password;
    public $reset = false;

    /**
     * The callback that should be used to create the reset password URL.
     *
     * @var (\Closure(mixed, string): string)|null
     */
    public static $createUrlCallback;

    /**
     * The callback that should be used to build the mail message.
     *
     * @var (\Closure(mixed, string): \Illuminate\Notifications\Messages\MailMessage)|null
     */
    public static $toMailCallback;

    protected $user;

    /**
     * Create a notification instance.
     *
     * @param  string  $token
     * @return void
     */
    public function __construct($password, $reset = false)
    {
        $this->password = $password;
        $this->reset = $reset;
    }

    /**
     * Get the notification's channels.
     *
     * @param  mixed  $notifiable
     * @return array|string
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        if (static::$toMailCallback) {
            return call_user_func(static::$toMailCallback, $notifiable, $this->password, $this->reset);
        }

        return $this->buildMailMessage($this->password, $this->reset);
    }


    public function updateMailContent($user)
    {
        $this->user = $user;
    }




    /**
     * Get the reset password notification mail message for the given URL.
     *
     * @param  string  $url
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    protected function buildMailMessage($password, $reset = false)
    {
        $templateName = $reset ? 'Reset Password' : 'User Registration';
        $template = $this->getEmailTemplate($templateName);

        $variables = $reset
            ? ['{{password}}' => $password, '{{app_name}}' => config('app.name')]
            : [
                '{{first_name}}' => $this->user->first_name,
                '{{last_name}}' => $this->user->last_name,
                '{{email}}' => $this->user->email,
                '{{password}}' => $password,
                '{{app_name}}' => config('app.name'),
            ];

        $subject = $this->replaceTemplateVariables($template->subject, $variables);
        $content = $this->replaceTemplateVariables($template->content, $variables);

        if (!$reset) {
            // Clean up mentions for user registration email only
            $content = preg_replace(
                '/<span class="mention" data-mention="[^"]*">([^<]*)<\/span>/',
                '$1',
                $content
            );
            Log::info("Email content", ['content' => $content]);
        }

        return (new MailMessage)
            ->subject($subject)
            ->line(new HtmlString($content));
    }

    protected function getEmailTemplate($name)
    {
        $template = EmailTemplate::where('name', $name)->first();

        if (!$template) {
            throw new \Exception("Email template '{$name}' not found or inactive.");
        }

        return $template;
    }

    protected function replaceTemplateVariables($text, array $variables)
    {
        return strtr($text, $variables);
    }


    /**
     * Set a callback that should be used when creating the reset password button URL.
     *
     * @param  \Closure(mixed, string): string  $callback
     * @return void
     */
    public static function createUrlUsing($callback)
    {
        static::$createUrlCallback = $callback;
    }

    /**
     * Set a callback that should be used when building the notification mail message.
     *
     * @param  \Closure(mixed, string): \Illuminate\Notifications\Messages\MailMessage  $callback
     * @return void
     */
    public static function toMailUsing($callback)
    {
        static::$toMailCallback = $callback;
    }
}
