<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserMatchStreaming extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'match_id',
        'token',
        'role',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function match()
    {
        return $this->belongsTo(StageMatch::class);
    }

    public function scopeCameramans($query)
    {
        return $query->where('role', 'cameraman');
    }

    public function scopeControllers($query)
    {
        return $query->where('role', 'controller');
    }
}
