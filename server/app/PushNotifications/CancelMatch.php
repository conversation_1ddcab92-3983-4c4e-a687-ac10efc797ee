<?php

namespace App\PushNotifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

class CancelMatch extends Notification implements ShouldQueue
{
    use Queueable;
    public $match;
    public $homeTeam;
    public $awayTeam;
    public $location;
    public $startTime;
    public function via($notifiable)
    {
        return [FcmChannel::class];
    }

    public function __construct($match)
    {
        $this->match = $match;
        $this->homeTeam = $match->homeTeam ? $match->homeTeam->name : 'TBD';
        $this->awayTeam = $match->awayTeam ? $match->awayTeam->name : 'TBD';
        $this->location = $match->location ? $match->location->name : 'TBD';
        $this->startTime = $match->start_time ? $match->start_time : 'TBD';
    }

    public function toFcm($notifiable): FcmMessage
    {
        $match_details_url = config('constants.match_details_url');
        $match_details_url = str_replace('{match_id}', $this->match->id, $match_details_url);

        return (new FcmMessage(notification: new FcmNotification(
            title: $this->homeTeam . ' VS ' . $this->awayTeam . ' at ' . $this->location . ' has been cancelled.',
            body: $this->match->description ? $this->match->description : __('You have been notified about this match cancellation'),
        )))
            ->data(['go_url' => $match_details_url])
            ->custom([
                'android' => [
                    'notification' => [
                        'color' => '#0A0A0A',
                    ],
                    'fcm_options' => [
                        'analytics_label' => 'analytics',
                    ],
                    'collapse_key' => 'cancel_match',
                ],
                'apns' => [
                    'fcm_options' => [
                        'analytics_label' => 'analytics_ios',
                    ],
                ],
                'webpush' => [
                    'fcm_options' => [
                        'analytics_label' => 'analytics_web',
                        'link' => config('app.client_url'),
                    ],
                ],
            ]);
    }
}
