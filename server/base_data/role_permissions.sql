/*
 Navicat Premium Data Transfer

 Source Server         : WEBHOST
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-google-log)
 Source Host           : *************:3306
 Source Schema         : ezleague

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-google-log)
 File Encoding         : 65001

 Date: 29/02/2024 09:40:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` int(10) UNSIGNED NOT NULL,
  `permission_id` int(10) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  <PERSON><PERSON>AR<PERSON> KEY (`id`) USING BTREE,
  INDEX `role_permissions_role_id_foreign`(`role_id`) USING BTREE,
  INDEX `role_permissions_permission_id_foreign`(`permission_id`) USING BTREE,
  CONSTRAINT `role_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `role_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_permissions
-- ----------------------------
INSERT INTO `role_permissions` VALUES (1, 2, 12, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (2, 2, 21, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (3, 2, 22, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (4, 2, 23, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (5, 2, 24, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (6, 2, 31, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (7, 2, 32, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (8, 2, 33, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (9, 2, 34, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (10, 2, 41, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (11, 2, 42, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (12, 2, 43, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (13, 2, 44, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (14, 2, 45, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (15, 2, 46, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (16, 2, 47, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (17, 2, 48, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (18, 2, 49, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (19, 3, 11, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (20, 3, 22, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (21, 3, 23, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (22, 3, 24, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (23, 3, 32, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (24, 3, 34, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (25, 4, 11, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (26, 4, 22, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (27, 4, 24, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (28, 4, 32, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (29, 4, 34, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (30, 5, 11, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (31, 5, 34, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (32, 6, 34, '2024-02-22 06:26:25', '2024-02-22 06:26:25');
INSERT INTO `role_permissions` VALUES (33, 7, 34, '2024-02-22 06:26:25', '2024-02-22 06:26:25');

SET FOREIGN_KEY_CHECKS = 1;
