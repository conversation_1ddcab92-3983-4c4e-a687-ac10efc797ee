<?php

use Illuminate\Support\Facades\Facade;

return [

    'azure' => [
        'account_name' => env('AZURE_ACCOUNT_NAME', 'ezleague'),
        'account_key' => env('AZURE_ACCOUNT_KEY', '****************************************************************************************'),
        'container_name' => env('AZURE_CONTAINER_NAME', 'video'),
    ],

    // 'livekit' => [
    //     // 'host' => env('LIVEKIT_HOST', 'https://ezleague-hrbztjul.livekit.cloud'),
    //     // 'api_key' => env('LIVEKIT_API_KEY', 'APIzqrYDF4yFg39'),
    //     // 'api_secret' => env('LIVEKIT_API_SECRET', 'rCmS6DffP49vKw53tS6kIhGTWt3YdYuHvZ5R16OYcUE')
    // ],

    // USER ROLE
    'role_base' => [
        'admin' => env('ADMIN_ROLE', 2),
        'parent' => env('PARENT_ROLE', 5),
        'player' => env('PLAYER_ROLE', 6),
        'user' => env('USER_ROLE', 8)
    ],

    // GROUP TYPE
    'group_type' => [
        'mixed' => 'Mixed',
        'boys' => 'Boys',
        'girls' => 'Girls'
    ],

    //
    'gender' => [
        'male' => 'Male',
        'female' => 'Female'
    ],

    // Send Message Type
    'send_message_type' => [
        'email' => 'Email',
        'push_noti' => 'Push Notification',
        'email_push_noti' => 'Email & Push Notification'
    ],

    //VALIDATE STATUS
    'validate_status' => [
        'pending' => 'Pending',
        'awaiting_update' => 'Awaiting Update',
        'validated' => 'Validated',
        'updated' => 'Updated',
    ],

    // APPROVE STATUS
    'approve_status' => [
        'registered' => 'Registered',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'cancelled' => 'Cancelled',
        'pending' => 'Pending',
        'payment_error' => 'Payment Error'
    ],

    // PLAYER UPDATE STATUS
    'player_update_status' => [
        'pending' => 'Pending',
        'rejected' => 'Rejected',
        'approved' => 'Approved'
    ],

    // SEASON STATUS
    'season_status' => [
        'active' => 'Active',
        'archived' => 'Archived'
    ],

    // TYPE OF TOURNAMENT
    'tournament_types' => [
        'league' => 'League',
        'groups_knockouts' => 'Groups + Knockout(s)',
        'league_knockout' => 'League + Knockout',
        'knockout' => 'Knockout',
        'groups' => 'Groups'
    ],

    // RANKING_CRITERIA
    'ranking_criteria' => [
        'total' => 'Total',
        'direct_matches' => 'Direct matches',
        'head_to_head' => 'Head to Head',
    ],

    // KNOCKOUT_MODES
    'knockout_modes' => [
        'single' => [
            'value' => 1,
            'label' => 'Single elimination',
        ],
        'double' => [
            'value' => 2,
            'label' => 'Double elimination',
        ],
    ],

    // MATCH_DETAIL_TYPES
    'match_detail_types' => [
        'goal' => 'Goal',
        'yellow_card' => 'Yellow Card',
        'red_card' => 'Red Card',
        'substitution' => 'Substitution',
        'penalty' => 'Penalty',
        'own_goal' => 'Own goal',
        'injury' => 'Injury',
        'other' => 'Other',
    ],

    'cancel_match_types' => [
        'Postponed',
        'Cancelled',
        'Abandoned',
        'Rescheduled',
    ],

    'settings_keys' => [
        'smtp' => 'smtp_account',
        'required_versions' => 'r_version',
        'init_json' => 'init_json',
        'notification' => 'notification',
        "payment" => "payment_settings",
        "policy_notification" => "policy_notification",
        "metadata" => "metadata",
        "livekit" => "livekit",
    ],

    'match_details_url' => env('MATCH_DETAILS_URL', '/leagues/matches/{match_id}/details'),

    // PAYMENT METHOD
    'payment_method' => env('PAYMENT_METHOD', 'paypal'),

    'payment_currency' => env('CASHIER_CURRENCY', 'HKD'),

    // PAYMENT DETAILS TYPE
    'payment_details_type' => [
        'registration' => 'registration',
        'other' => 'other'
    ],

    'is_require_payment' => env('IS_REQUIRE_PAYMENT', true),
    'app_name' => env('APP_SHORT_NAME', 'EZL'),

    // PAYMENT STATUS
    'payment_status' => [
        'pending' => 'pending',
        'open' => 'open',
        'sent' => 'sent',
        'paid' => 'paid',
        'marked_as_paid' => 'marked as paid',
        'unpaid' => 'unpaid',
        'failed' => 'failed',
        'cancelled' => 'cancelled',
        'succeeded' => 'succeeded',
        'refunded' => 'refunded',
        'in_progress' => 'in progress',
        'partially_refunded' => 'partially refunded',
        'marked_as_refunded' => 'marked as refunded',
    ],

    'payment_status_paid' => [
        'paid',
        'succeeded',
    ],

    'payment_status_sent' => [
        'open',
        'sent',
    ],

    'payment_status_refund' => [
        'refunded',
        'partially_refunded',
        'marked_as_refunded',
    ],

    'payment_status_refunded' => [
        'refunded',
        'marked_as_refunded',
    ],

    'payment_note_types' => [
        'REFUND' => 'REFUND',
        'CANCEL' => 'CANCEL',
    ],

    // Minimum amount of payment
    'min_amount_payment' => [
        'stripe' => [
            'USD' => 0.5,
            'AED' => 2.0,
            'AUD' => 0.5,
            'BGN' => 1.0,
            'BRL' => 0.5,
            'CAD' => 0.5,
            'CHF' => 0.5,
            'CZK' => 15.0,
            'DKK' => 2.5,
            'EUR' => 0.5,
            'GBP' => 0.3,
            'HKD' => 4.0,
            'HRK' => 0.5,
            'HUF' => 175.0,
            'INR' => 0.5,
            'JPY' => 50.0,
            'MXN' => 10.0,
            'MYR' => 2.0,
            'NOK' => 3.0,
            'NZD' => 0.5,
            'PLN' => 2.0,
            'RON' => 2.0,
            'SEK' => 3.0,
            'SGD' => 0.5,
            'THB' => 10.0,
        ],
        'paypal' => [
            'USD' => 0.5,
            'AED' => 2.0,
            'AUD' => 0.5,
            'BGN' => 1.0,
            'BRL' => 0.5,
            'CAD' => 0.5,
            'CHF' => 0.5,
            'CZK' => 15.0,
            'DKK' => 2.5,
            'EUR' => 0.5,
            'GBP' => 0.3,
            'HKD' => 4.0,
            'HRK' => 0.5,
            'HUF' => 175.0,
            'INR' => 0.5,
            'JPY' => 50.0,
            'MXN' => 10.0,
            'MYR' => 2.0,
            'NOK' => 3.0,
            'NZD' => 0.5,
            'PLN' => 2.0,
            'RON' => 2.0,
            'SEK' => 3.0,
            'SGD' => 0.5,
            'THB' => 10.0,
        ]
    ],
    'sport_types' => [
        'football' => [
            'points_win' => 3,
            'points_draw' => 1,
            'points_loss' => 0,
        ],
        'basketball' => [
            'points_win' => 2,
            'points_loss' => 0,
        ],
        'rugby' => [
            'points_win' => 4,
            'points_draw' => 2,
            'points_loss' => 0,
        ],
        'baseball' => [
            'points_win' => 1,
            'points_loss' => 0,
        ],
        'american_football' => [
            'points_win' => 6,
            'points_draw' => 3,
            'points_loss' => 0,
        ],
    ],
    'type_knockout' => [
        'type_1' => ['1v1, 2v2, 3v3, 4v4'],
        'type_2' => ['1v2, 2v1, 3v4, 4v3'],
        'type_3' => ['1v2, 2v1'],
    ],

    'max_upload_size' => 5, // Nums of MB -> 5 for 5MB

    'regex' => [
        'url' => "/^https?:\/\//",
    ]
];
