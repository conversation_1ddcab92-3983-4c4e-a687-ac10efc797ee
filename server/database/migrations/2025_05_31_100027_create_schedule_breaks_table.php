<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateScheduleBreaksTable extends Migration
{
    public function up(): void
    {
        Schema::create('schedule_breaks', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('tournament_id');
            $table->unsignedInteger('location_id');
            $table->unsignedBigInteger('time_slot_id')->nullable();
            $table->string('description')->nullable();
            $table->timestamps();

            $table->foreign('tournament_id')->references('id')->on('tournaments')->onDelete('cascade');
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('cascade');
            $table->foreign('time_slot_id')->references('id')->on('schedule_time_slots')->onDelete('cascade');

            $table->index(['tournament_id', 'location_id', 'time_slot_id'], 'idx_schedule_breaks_time');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('schedule_breaks');
    }
}
