<?php

namespace Database\Seeders;

use Illuminate\Support\Facades\Log;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UserMessageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();
        DB::table('user_messages')->truncate();

        $env = env('APP_ENV');
        $sql_path = database_path("sql/$env/user_messages.sql");

        if (file_exists($sql_path)) {
            $sql = file_get_contents($sql_path);
            if (!empty($sql)) {
                $arr_queries = explode(';', $sql);
                $queries = array_chunk($arr_queries, 1000);
                foreach ($queries as $query) {
                    if (!empty($query)) {
                        DB::unprepared(implode(';', $query));
                    }
                }
            } else {
                Log::info("Empty SQL file: $sql_path");
            }
        } else {
            Log::info("File not found: $sql_path");
        }

        Schema::enableForeignKeyConstraints();
    }
}
