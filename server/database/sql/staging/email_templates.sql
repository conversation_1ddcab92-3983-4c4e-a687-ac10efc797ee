INSERT INTO `email_templates` (`id`, `name`, `subject`, `content`, `is_active`, `variables`, `created_at`, `updated_at`) VALUES
(4, 'User Registration', 'Welcome to Our Service, {{first_name}}', '<p>Dear <span class=\"mention\" data-mention=\"{{first_name}}\">{{first_name}}</span> <span class=\"mention\" data-mention=\"{{last_name}}\">{{last_name}}</span>&nbsp;</p><p>Your account has been created successfully.</p><p>Your login details:</p><ul><li>Email: <span class=\"mention\" data-mention=\"{{email}}\">{{email}}</span>&nbsp;</li><li>Password: <span class=\"mention\" data-mention=\"{{password}}\">{{password}}</span>&nbsp;</li></ul><p>Please change your password after login.</p><p>Best regards,<br>The Team</p>', 1, '[{\"key\": \"first_name\", \"description\": \"First Name\"}, {\"key\": \"last_name\", \"description\": \"Last Name\"}, {\"key\": \"email\", \"description\": \"Email\"}, {\"key\": \"password\", \"description\": \"Password\"}]', '2025-06-10 15:17:09', '2025-06-11 09:49:15'),

(6, 'Reset Password', '{{app_name}}  - Your password has been reset', '<p>Your password has been reset to <span class=\"mention\" data-mention=\"{{password}}\">{{password}}</span>&nbsp;</p><p>Please login and go to <i>User Profile</i> to change your password.</p>', 1, '[{\"key\": \"password\", \"description\": \"New Password\"}]', '2025-06-10 15:17:09', '2025-06-11 09:49:31'),

(7, 'Player Registration Incomplete', '{{app_name}} - Player registration incomplete: information required', '<p>The following information is required to complete the registration of <span class=\"mention\" data-mention=\"{{player_name}}\">{{player_name}}</span> for the season <span class=\"mention\" data-mention=\"{{season_name}}\">{{season_name}}</span>.</p><p>Please login to your account and complete the registration.</p><p>The following information need to check:</p><p><span style=\"color:rgb(0,0,0);\">&nbsp;{{fields_not_accepted}}</span></p>', 1, '[{\"key\": \"guardian_name\", \"description\": \"Guardian Full Name\"}, {\"key\": \"player_name\", \"description\": \"Player Full Name\"}, {\"key\": \"season_name\", \"description\": \"Season Name\"}]', '2025-06-10 15:17:09', '2025-06-12 04:12:39'),
(8, 'Cancel Registration', 'Registration for {{player_name}} has been cancelled', '<p>Dear <span class="mention" data-mention="{{guardian_name}}">{{guardian_name}}</span>,</p><p>The registration for <span class="mention" data-mention="{{player_name}}">{{player_name}}</span> in season <span class="mention" data-mention="{{season_name}}">{{season_name}}</span> has been cancelled.</p><p><strong>Reason:</strong> <span class="mention" data-mention="{{reason}}">{{reason}}</span></p><p>If you have any questions, please contact us.</p><p>Best regards,<br>The Team</p>', 1, '[{\"key\": \"player_name\", \"description\": \"Player Full Name\"}, {\"key\": \"season_name\", \"description\": \"Season Name\"}, {\"key\": \"guardian_name\", \"description\": \"Guardian Full Name\"}, {\"key\": \"reason\", \"description\": \"Cancellation Reason\"}]', NOW(), NOW()),
(9, 'Registration Approved with Payment', '{{app_name}} - Registration for Season {{season_name}} has been approved.', '<p>Dear <span class=\"mention\" data-mention=\"{{guardian_name}}\">{{guardian_name}}</span> &nbsp;,</p><p>Your registration for <span class=\"mention\" data-mention=\"{{player_name}}\">{{player_name}}</span> &nbsp; has been approved.</p><p>A new invoice with the amount of <span class=\"mention\" data-mention=\"{{fee}}\">{{fee}}</span> &nbsp;has been generated for you. Please click on the link below to make the payment.</p><p><a href=\"{{payment_url}}\">Pay now</a></p><hr><p>If you\'re having trouble clicking the \"Pay now\" button, copy and paste the URL below into your web browser:</p><p><span class=\"mention\" data-mention=\"{{payment_url}}\">{{payment_url}}</span>&nbsp;</p>', 1, '[{\"key\": \"guardian_name\", \"description\": \"Guardian Full Name\"}, {\"key\": \"player_name\", \"description\": \"Player Full Name\"}, {\"key\": \"season_name\", \"description\": \"Season Name\"}, {\"key\": \"fee\", \"description\": \"Invoice Amount\"}, {\"key\": \"payment_url\", \"description\": \"Payment Link\"}]', '2025-06-10 16:23:14', '2025-06-11 09:51:10'),

(10, 'Registration Approved No Payment', '{{app_name}} - Registration for Season {{season_name}} has been approved.', '<p>Dear <span class=\"mention\" data-mention=\"{{guardian_name}}\">{{guardian_name}}</span>,</p><p>Your registration for <span class=\"mention\" data-mention=\"{{player_name}}\">{{player_name}}</span> &nbsp; has been approved.</p><p>You can now log in to the app and view your player\'s information.</p><p>Best regards,<br>The Team</p>', 1, '[{\"key\": \"guardian_name\", \"description\": \"Guardian Full Name\"}, {\"key\": \"player_name\", \"description\": \"Player Full Name\"}, {\"key\": \"season_name\", \"description\": \"Season Name\"}]', '2025-06-10 16:23:14', '2025-06-11 09:51:45');
