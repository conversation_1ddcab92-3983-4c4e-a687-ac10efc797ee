# Use the official Ubuntu 22 image as the base image
FROM ubuntu:22.04

# Add the ondrej/php repository
RUN apt-get update && \
    apt-get install -y software-properties-common && \
    add-apt-repository ppa:ondrej/php && \
    apt-get update

ENV TZ=Asia/Bangkok
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install necessary packages
RUN apt-get install -y \
        php8.2 \
        php8.2-cli \
        php8.2-bz2 \
        php8.2-curl \
        php8.2-mbstring \
        php8.2-intl \
        php8.2-mysql \
        php8.2-zip \
        php8.2-gd \
        libapache2-mod-php8.2 \
        composer

# Enable SSL module for Apache
RUN a2enmod ssl && a2enmod php8.2

# Generate a self-signed SSL certificate (replace with your own certificate in a production environment)
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout /etc/ssl/private/apache-selfsigned.key -out /etc/ssl/certs/apache-selfsigned.crt -subj "/C=US/ST=State/L=City/O=Organization/CN=example.com"

# Configure PHP settings

# Install php-xml separately
RUN apt-get install -y \
 php8.2-xml \ 
 nano


# Enable and configure PHP FPM
RUN a2ensite default-ssl

# Expose ports 80 and 443 for HTTP and HTTPS respectively
EXPOSE 80
EXPOSE 443

# Start Apache in the foreground
CMD ["apachectl", "-D", "FOREGROUND"]
