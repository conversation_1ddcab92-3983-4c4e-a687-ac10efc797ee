    /* create variable */
    :root {
        --primary-color: #00898c;
        --secondary-color: #051c1c;
        --accent-color: #f8e71c;
    }

    body {
        overflow: hidden;
        background-color: transparent !important;
        font-family: 'Noto Sans TC', sans-serif;
    }

    h1,
    h2,
    h3 {
        margin: 0 !important;
    }

    .name {
        color: var(--accent-color);
    }

    .scoreboard_container {
        width: 650px;
        /* height: 160px; */
        background-color: blue;
    }

    .home_score_wrapper {
        position: absolute !important;
        border-right: 1px solid black;
        top: 0;
        width: 50% !important;
        height: 100%;
        overflow: hidden;
        background-color: var(--accent-color);
        z-index: 2;
    }

    .away_score_wrapper {
        position: absolute !important;
        border-left: 1px solid black;
        left: 50%;
        top: 0;
        width: 50% !important;
        height: 100%;
        overflow: hidden;
        background-color: var(--accent-color);
        z-index: 2;
    }

    #home_score {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    #away_score {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .screen {
        background-color: transparent !important;
        visibility: inherit;
        width: 1920px;
        height: 1080px;
        z-index: 0;
        position: absolute;
        overflow: hidden;
        touch-action: none;
        user-select: none;
        -webkit-user-drag: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    .scoreboard {
        background-color: var(--secondary-color);
    }

    .scores {
        width: 25%;
        max-width: 160px !important;
        background-color: var(--accent-color);
    }

    .timer_wrapper {
        position: absolute;
        z-index: 1;
        background-color: var(--accent-color);
    }

    .stoppage-time-wraper {
        position: absolute;
        background-color: var(--primary-color);
        right: -50px;
        width: 50px;
        color: white;
    }

    .score {
        color: var(--secondary-color);
        position: relative !important;
    }

    .logo img {
        width: 60px;
        height: 60px;
    }

    .split-score {
        width: 3px;
        height: 50px;
        margin: 0 10px;
        background-color: black;
    }

    .home_score_in {
        animation: home_score_in 0.3s ease-in-out;
    }

    .home_score_out {
        animation: home_score_out 0.3s ease-in-out;
    }

    .away_score_in {
        animation: away_score_in 0.3s ease-in-out;
    }

    .away_score_out {
        animation: away_score_out 0.3s ease-in-out;
    }

    .hide_scoreboard {
        animation: scroll_to_hide 0.4s ease-in-out;
    }

    .show_scoreboard {
        animation: scroll_to_show 0.4s ease-in-out;
    }

    @keyframes home_score_out {
        0% {
            left: 150%;
        }

        100% {
            left: 50%;
        }
    }

    @keyframes home_score_in {
        0% {
            left: 50%;
        }

        100% {
            left: 150%;
        }
    }

    @keyframes away_score_out {
        0% {
            left: -50%;
        }

        100% {
            left: 50%;
        }
    }

    @keyframes away_score_in {
        0% {
            left: 50%;
        }

        100% {
            left: -50%;
        }
    }


    /* Scroll Scoreboard to the left */
    @keyframes scroll_to_hide {
        0% {
            transform: translateX(0%);
        }

        100% {
            transform: translateX(-100%);
        }
    }

    /* Scroll Scoreboard to the right */
    @keyframes scroll_to_show {
        0% {
            transform: translateX(-100%);
        }

        100% {
            transform: translateX(0%);
        }
    }

    /* hide timer */
    .hide_timer {
        animation: hide_timer 0.2s ease-in-out;
    }

    .show_timer {
        animation: show_timer 0.2s ease-in-out;
    }

    /* scroll timer to the top */
    @keyframes hide_timer {
        0% {
            transform: translateY(0%);
        }

        100% {
            transform: translateY(-150%);
        }
    }

    /* scroll timer to the bottom */
    @keyframes show_timer {
        0% {
            transform: translateY(-100%);
        }

        100% {
            transform: translateY(0%);
        }
    }

    /* hide stoppage time */
    .hide_stoppage_time {
        animation: scroll_to_hide 0.2s ease-in-out;
    }

    .show_stoppage_time {
        animation: scroll_to_show 0.2s ease-in-out;
    }