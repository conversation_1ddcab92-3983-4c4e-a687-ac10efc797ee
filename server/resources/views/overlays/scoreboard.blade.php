<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <!-- inport bootstrap 4 cdn-->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- import jquery cdn-->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <!-- import popper cdn-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
    <!-- import file styles.css -->
    <link rel="stylesheet" href="{{ asset('/css/nomalize.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="">
    <link href="{{ asset('/css/overlays/styles.css') }}" rel="stylesheet">
    <style>
        /* create variable */
        :root {
            --primary-color: #00898c;
            --secondary-color: #051c1c;
            --accent-color: #f8e71c;
        }

        body {
            overflow: hidden;
            background-color: transparent !important;
            font-family: 'Noto Sans TC', sans-serif;
        }

        h1,
        h2,
        h3 {
            margin: 0 !important;
        }

        .name {
            color: var(--accent-color);
        }

        .scoreboard_container {
            width: 650px;
            /* height: 160px; */
            background-color: blue;
        }

        .home_score_wrapper {
            position: absolute !important;
            border-right: 1px solid black;
            top: 0;
            width: 50% !important;
            height: 100%;
            overflow: hidden;
            background-color: var(--accent-color);
            z-index: 2;
        }

        .away_score_wrapper {
            position: absolute !important;
            border-left: 1px solid black;
            left: 50%;
            top: 0;
            width: 50% !important;
            height: 100%;
            overflow: hidden;
            background-color: var(--accent-color);
            z-index: 2;
        }

        #home_score {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        #away_score {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .screen {
            background-color: transparent !important;
            visibility: inherit;
            width: 1920px;
            height: 1080px;
            z-index: 0;
            position: absolute;
            overflow: hidden;
            touch-action: none;
            user-select: none;
            -webkit-user-drag: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }

        .scoreboard {
            background-color: var(--secondary-color);
        }

        .scores {
            width: 25%;
            max-width: 160px !important;
            background-color: var(--accent-color);
        }

        .timer_wrapper {
            position: absolute;
            z-index: 1;
            background-color: var(--accent-color);
        }

        .stoppage-time-wraper {
            position: absolute;
            background-color: var(--primary-color);
            right: -50px;
            width: 50px;
            color: white;
        }

        .score {
            color: var(--secondary-color);
            position: relative !important;
        }

        .logo img {
            width: 60px;
            height: 60px;
        }

        .split-score {
            width: 3px;
            height: 50px;
            margin: 0 10px;
            background-color: black;
        }

        .home_score_in {
            animation: home_score_in 0.3s ease-in-out;
        }

        .home_score_out {
            animation: home_score_out 0.3s ease-in-out;
        }

        .away_score_in {
            animation: away_score_in 0.3s ease-in-out;
        }

        .away_score_out {
            animation: away_score_out 0.3s ease-in-out;
        }

        .hide_scoreboard {
            animation: scroll_to_hide 0.4s ease-in-out;
        }

        .show_scoreboard {
            animation: scroll_to_show 0.4s ease-in-out;
        }

        @keyframes home_score_out {
            0% {
                left: 150%;
            }

            100% {
                left: 50%;
            }
        }

        @keyframes home_score_in {
            0% {
                left: 50%;
            }

            100% {
                left: 150%;
            }
        }

        @keyframes away_score_out {
            0% {
                left: -50%;
            }

            100% {
                left: 50%;
            }
        }

        @keyframes away_score_in {
            0% {
                left: 50%;
            }

            100% {
                left: -50%;
            }
        }


        /* Scroll Scoreboard to the left */
        @keyframes scroll_to_hide {
            0% {
                transform: translateX(0%);
            }

            100% {
                transform: translateX(-100%);
            }
        }

        /* Scroll Scoreboard to the right */
        @keyframes scroll_to_show {
            0% {
                transform: translateX(-100%);
            }

            100% {
                transform: translateX(0%);
            }
        }

        /* hide timer */
        .hide_timer {
            animation: hide_timer 0.2s ease-in-out;
        }

        .show_timer {
            animation: show_timer 0.2s ease-in-out;
        }

        /* scroll timer to the top */
        @keyframes hide_timer {
            0% {
                transform: translateY(0%);
            }

            100% {
                transform: translateY(-150%);
            }
        }

        /* scroll timer to the bottom */
        @keyframes show_timer {
            0% {
                transform: translateY(-100%);
            }

            100% {
                transform: translateY(0%);
            }
        }

        /* hide stoppage time */
        .hide_stoppage_time {
            animation: scroll_to_hide 0.2s ease-in-out;
        }

        .show_stoppage_time {
            animation: scroll_to_show 0.2s ease-in-out;
        }
    </style>
</head>

<body>
    <div class="screen">
        <div class="scoreboard_container">
            <div class="row w-100 p-0 m-0 scoreboard">
                <div class="home_team col pt-0 pb-0 pl-0 pr-50 d-flex align-items-center">
                    <div class="logo">
                        <img src="overlays/1.jpg" alt="" id="home_logo">
                    </div>
                    <div class="name flex-grow-1">
                        <h1 class="text-center text-nowrap" id="home_name">HOME</h1>
                    </div>
                </div>
                <div class="scores col-3 p-0">
                    <div class="score d-flex h-100 p-0 align-items-center ">
                        <div class="home_score_wrapper col p-0">
                            <h1 class="text-center" id="home_score">Ana</h1>
                        </div>
                        <div class="away_score_wrapper col p-0">
                            <h1 class="text-center" id="away_score">Bel</h1>
                        </div>
                    </div>
                    <div class="timer_wrapper w-100 h-75">
                        <div class="text-center d-flex align-items-center justify-content-center">
                            <span class="h2 mr-1" id="period">1st</span>
                            <span class="h2" id="timer">00:00</span>
                        </div>

                    </div>
                    <div class="stoppage-time-wraper h-75">
                        <div class="d-flex align-items-center justify-content-center">
                            <h3 class="text-center">+<span id="stoppage_time">1</span></h3>
                        </div>
                    </div>
                </div>
                <div class="away_team col pt-0 pb-0 pl-50 pr-0 d-flex align-items-center">
                    <div class="name flex-grow-1">
                        <h1 class="text-center  text-nowrap" id="away_name">AWAY</h1>
                    </div>
                    <div class="logo">
                        <img src="overlays/2.jpg" alt="" id="away_logo">
                    </div>
                </div>
            </div>
        </div>
    </div>



</body>
<script>
    let period_scoreboard_time = 500;
    let period_hide_scoreboard = 400;
    let period_score_time = 300;
    let period_timer_time = 200;
    let period_stoppage_time = 200;
    let isShowScoreboard = false;
    let isShowTimer = false;
    let isShowStoppageTime = true;


    // scale element .screen to fit window but keep aspect ratio    transform: scale(0.490833, 0.490833) translate(0px, 0px);
    $(document).ready(function() {
        let screen = $(".screen");
        resizeScreen();
        resizeScreen();
        // list window change size
        $(window).resize(function() {
            resizeScreen();
        });
        let scoreboard_container = document.getElementsByClassName("scoreboard_container")[0];
        scoreboard_container.classList.add("d-none");
        hideStoppageTime();
        hideTimer();
        hideScoreboard();
        setTimeout(function() {
            scoreboard_container.classList.remove("d-none");
            toggleScoreboard();
        }, 1000);

        function resizeScreen() {
            var width = $(window).width();
            var height = $(window).height();
            var ratio_scale = width / 1920;
            var ratio_height_scale = height / 1080;
            if (ratio_scale > ratio_height_scale) {
                ratio_scale = ratio_height_scale;
            }
            // calculate top and left to center screen
            var top = (window.innerHeight - 1080 * ratio_scale) / 2;
            var left = (window.innerWidth - 1920 * ratio_scale) / 2;
            screen.css("transform", "scale(" + ratio_scale + "," + ratio_scale + ") translate(0px, 0px)");
            screen.css("transform-origin", "0 0");
            screen.css("top", top + "px");
            screen.css("left", left + "px");
        }
    });

    // SCORES
    function homeScoreChange(value) {
        var score = document.getElementById("home_score");
        score.classList.add("home_score_in");
        setTimeout(function() {
            score.classList.remove("home_score_in");
            score.classList.add("home_score_out");
            // set score text
            if (value != undefined) {
                score.innerText = value;
            }

            setTimeout(function() {
                score.classList.remove("home_score_out");
            }, period_score_time);

        }, period_score_time);
    }

    async function awayScoreChange(value) {
        var score = document.getElementById("away_score");
        score.classList.add("away_score_in");
        await delay(period_score_time);
        score.classList.remove("away_score_in");
        score.classList.add("away_score_out");
        // set score text
        if (value != undefined) {
            score.innerText = value;
        }
        await delay(period_score_time);
        score.classList.remove("away_score_out");
    }


    // SCOREBOARD
    function toggleScoreboard() {
        isShowScoreboard = !isShowScoreboard;
        if (isShowScoreboard) {
            showScoreboard();
            setTimeout(function() {
                toggleTimer(isShowScoreboard);
            }, period_timer_time + period_scoreboard_time);
        } else {
            toggleTimer(isShowScoreboard);
            setTimeout(function() {
                hideScoreboard();
            }, period_hide_scoreboard);
        }
    }

    function hideScoreboard() {
        var scoreboard = document.getElementsByClassName("scoreboard_container")[0];
        scoreboard.classList.add("hide_scoreboard");
        isShowScoreboard = false;
        setTimeout(function() {
            scoreboard.classList.add("d-none");
            scoreboard.classList.remove("hide_scoreboard");
        }, period_hide_scoreboard);
    }

    function showScoreboard() {
        var scoreboard = document.getElementsByClassName("scoreboard_container")[0];
        scoreboard.classList.remove("d-none");
        scoreboard.classList.add("show_scoreboard");
        isShowScoreboard = true;
        setTimeout(function() {
            scoreboard.classList.remove("show_scoreboard");
        }, period_hide_scoreboard);
    }

    // TIMER
    function toggleTimer(toggle) {
        isShowTimer = !isShowTimer;
        if (toggle != undefined) {
            isShowTimer = toggle;
        }
        if (isShowTimer) {
            if (isShowStoppageTime) {
                showTimer();
                setTimeout(function() {
                    showStoppageTime();
                }, period_stoppage_time);
            } else {
                showTimer();
            }
        } else {
            if (isShowStoppageTime) {
                hideStoppageTime();
                setTimeout(function() {
                    hideTimer();
                }, period_stoppage_time);
            } else {
                hideTimer();
            }
        }
    }

    function showTimer() {
        var timer = document.getElementsByClassName("timer_wrapper")[0];
        timer.classList.remove("d-none");
        timer.classList.add("show_timer");
        isShowTimer = true;
        setTimeout(function() {
            timer.classList.remove("show_timer");
        }, period_timer_time);
    }

    function hideTimer() {
        var timer = document.getElementsByClassName("timer_wrapper")[0];
        timer.classList.add("hide_timer");
        isShowTimer = false;
        setTimeout(function() {
            timer.classList.add("d-none");
            timer.classList.remove("hide_timer");
        }, period_timer_time);
    }

    // STOPPAGE TIME
    function toggleStoppageTime() {
        isShowStoppageTime = !isShowStoppageTime;
        if (isShowStoppageTime) {
            showStoppageTime();
        } else {
            hideStoppageTime();
        }
    }

    function showStoppageTime() {
        var stoppageTime = document.getElementsByClassName("stoppage-time-wraper")[0];
        stoppageTime.classList.remove("d-none");
        stoppageTime.classList.add("show_stoppage_time");
        isShowStoppageTime = true;
        setTimeout(function() {
            stoppageTime.classList.remove("show_stoppage_time");
        }, period_stoppage_time);
    }

    function hideStoppageTime() {
        var stoppageTime = document.getElementsByClassName("stoppage-time-wraper")[0];
        stoppageTime.classList.add("hide_stoppage_time");
        setTimeout(function() {
            stoppageTime.classList.add("d-none");
            stoppageTime.classList.remove("hide_stoppage_time");
        }, period_stoppage_time);
    }

    // set home team name
    function setHomeTeamName(name) {
        var home_name = document.getElementById("home_name");
        home_name.innerText = name;
    }

    // set away team name
    function setAwayTeamName(name) {
        var away_name = document.getElementById("away_name");
        away_name.innerText = name;
    }

    // set period
    function setPeriod(period) {
        var period = document.getElementById("period");
        period.innerText = period;
    }

    // set timer
    function setTimer(time) {
        var timer = document.getElementById("timer");
        timer.innerText = time;
    }

    // set stoppage time
    function setStoppageTime(time) {
        var stoppageTime = document.getElementById("stoppage_time");
        stoppageTime.innerText = time;
    }

    // set home logo
    function setHomeLogo(src) {
        var home_logo = document.getElementById("home_logo");
        home_logo.src = src;
    }

    // set away logo
    function setAwayLogo(src) {
        var away_logo = document.getElementById("away_logo");
        away_logo.src = src;
    }

    const delay = (delayInms) => {
        return new Promise(resolve => setTimeout(resolve, delayInms));
    };
</script>

</html>
